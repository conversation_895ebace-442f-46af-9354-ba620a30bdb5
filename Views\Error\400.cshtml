﻿@{
    ViewData["Title"] = "400 - Bad Request";
    Layout = "_ErrorLayout";
    var errorMessage = ViewBag.ErrorMessage as string ?? "The request could not be understood or was missing required parameters.";
    var originalPath = ViewBag.OriginalPath as string;
    var originalQueryString = ViewBag.OriginalQueryString as string;
}

<div class="text-center mt-5">
    <h1 class="display-4 text-warning">400</h1>
    <p class="lead">Bad Request - The server could not process your request.</p>

    <div class="alert alert-warning text-start mt-4">
        <strong>Error:</strong> @errorMessage
        @if (!string.IsNullOrEmpty(originalPath))
        {
            <hr />
            <p class="mb-0"><strong>Original Path:</strong> @originalPath</p>
            @if (!string.IsNullOrEmpty(originalQueryString))
            {
                <p class="mb-0"><strong>Query String:</strong> @originalQueryString</p>
            }
        }
    </div>
</div>s