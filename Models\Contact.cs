namespace AdressBookAppWeb.Models
{
    using System;
    using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

public class Contact
{
    public int Id { get; set; }

    [Required]
    public int UserId { get; set; }

    [Required]
    public string FirstName { get; set; }

    public string? LastName { get; set; }

    [Obsolete("Use FirstName + LastName instead.")]
    public string Name => $"{FirstName} {LastName}".Trim();

    public string? Position { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public string? Company { get; set; }
    public string? ContactImage { get; set; }

    [Required]
    public bool IsStarred { get; set; }

    public string? Tags { get; set; }

    public DateTime? BirthDate { get; set; }

    [Required]
    public DateTime CreatedDate { get; set; }

    public DateTime? UpdatedDate { get; set; }

    public User User { get; set; }

    public int? GroupId { get; set; }
    public Group Group { get; set; }

    public ICollection<Notification> Notifications { get; set; }
}
}
