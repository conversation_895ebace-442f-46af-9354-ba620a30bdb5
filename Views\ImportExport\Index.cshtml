﻿@{
    ViewData["Title"] = "Import & Export Contacts";
}

<div class="container-fluid px-4">
    <h2 class="mt-4">Import & Export Contacts</h2>
    <p class="text-muted">Choose an option below to manage your contact data.</p>

    @* Success/Error/Warning Alerts *@
    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success">@TempData["Message"]</div>
    }
    @if (TempData["Warning"] != null)
    {
        <div class="alert alert-warning">@TempData["Warning"]</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <div class="row mt-4">
        <div class="col-lg-6 mb-4">
            <h4><i class="bi bi-upload"></i> Export Options</h4>
            <div class="d-grid gap-2">
                <a class="btn btn-outline-primary" asp-action="ExportToVcf">Export to VCF (.vcf)</a>
                <a class="btn btn-outline-primary" asp-action="ExportToExcel">Export to Excel (.xlsx)</a>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <h4><i class="bi bi-download"></i> Import Options</h4>
            <div class="d-grid gap-2">
                <a class="btn btn-outline-success" asp-action="ImportFromVcf">Import from VCF (.vcf)</a>
                <a class="btn btn-outline-success" asp-action="ImportFromExcel">Import from Excel (.xlsx)</a>
                <a class="btn btn-outline-danger" asp-action="ImportFromGoogle">Import from Google Contacts</a>
            </div>
        </div>
    </div>
</div>