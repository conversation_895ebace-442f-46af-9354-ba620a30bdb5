﻿@{
    ViewData["Title"] = "Admin Dashboard";
}

<div class="container-fluid px-4">
    <h1 class="mt-4">Dashboard</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item active">Admin Panel</li>
    </ol>

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-2">Total Users</h5>
                    <p class="card-text fs-4 fw-bold">@ViewBag.TotalUsers</p>
                </div>
                <div class="card-footer bg-transparent d-flex align-items-center justify-content-between">
                    <a asp-area="Admin" asp-controller="Users" asp-action="Index"
                       class="small text-primary stretched-link">View Details</a>
                    <div class="small text-primary"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-2">Contact Reports</h5>
                    <p class="card-text fs-4 fw-bold">@ViewBag.ContactReports</p>
                </div>
                <div class="card-footer bg-transparent d-flex align-items-center justify-content-between">
                    <a asp-area="Admin" asp-controller="ContactReports" asp-action="Index"
                       class="small text-primary stretched-link">View Details</a>
                    <div class="small text-primary"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-2">New Registrations</h5>
                    <p class="card-text fs-4 fw-bold">@ViewBag.NewRegistrations</p>
                </div>
                <div class="card-footer bg-transparent d-flex align-items-center justify-content-between">
                    <a asp-area="Admin" asp-controller="Users" asp-action="Index"
                       class="small text-primary stretched-link">View Details</a>
                    <div class="small text-primary"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-2">Logs</h5>
                    <p class="card-text fs-4 fw-bold">@ViewBag.Logs</p>
                </div>
                <div class="card-footer bg-transparent d-flex align-items-center justify-content-between">
                    <a asp-area="Admin" asp-controller="Logs" asp-action="Index"
                       class="small text-primary stretched-link">View Details</a>
                    <div class="small text-primary"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>
</div>