﻿@model AdressBookAppWeb.ViewModels.UserCreateViewModel

@{
    ViewData["Title"] = "Add New User";
    var currentUser = ViewBag.CurrentUser as AdressBookAppWeb.Models.User;
    bool isSuperAdmin = currentUser?.Role == AdressBookAppWeb.Models.Enums.UserRole.SuperAdmin;
}

<div class="container-fluid px-4">
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <h1 class="mt-4">Add New User</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item">
            <a asp-area="Admin" asp-controller="Dashboard" asp-action="Index">Dashboard</a>
        </li>
        <li class="breadcrumb-item">
            <a asp-area="Admin" asp-controller="Users" asp-action="Index">Users</a>
        </li>
        <li class="breadcrumb-item active">Add User</li>
    </ol>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            @if (!ViewData.ModelState.IsValid)
            {
                <div class="alert alert-danger">
                    <ul>
                        @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                        {
                            <li>@error.ErrorMessage</li>
                        }
                    </ul>
                </div>
            }

            <form method="post" asp-action="Add" enctype="multipart/form-data">
                @Html.AntiForgeryToken()

                <div class="mb-3">
                    <label asp-for="FirstName" class="form-label">First Name</label>
                    <input asp-for="FirstName" class="form-control" />
                    <span asp-validation-for="FirstName" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="LastName" class="form-label">Last Name</label>
                    <input asp-for="LastName" class="form-control" />
                    <span asp-validation-for="LastName" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="Email" class="form-label">Email</label>
                    <input asp-for="Email" class="form-control" />
                    <span asp-validation-for="Email" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="Phone" class="form-label">Phone</label>
                    <input asp-for="Phone" class="form-control" />
                    <span asp-validation-for="Phone" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="Password" class="form-label">Password</label>
                    <input asp-for="Password" class="form-control" type="password" />
                    <span asp-validation-for="Password" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label class="form-label">Role</label>
                    @if (isSuperAdmin)
                    {
                        <select asp-for="Role" class="form-select">
                            <option value="">Select Role</option>
                            <option value="Admin" selected="@(Model.Role == AdressBookAppWeb.Models.Enums.UserRole.Admin ? "selected" : null)">Admin</option>
                            <option value="User" selected="@(Model.Role == AdressBookAppWeb.Models.Enums.UserRole.User ? "selected" : null)">User</option>
                        </select>
                    }
                    else
                    {
                        <input type="text" class="form-control" value="User" disabled />
                        <input type="hidden" name="Role" value="User" />
                    }
                    <span asp-validation-for="Role" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label for="profileImage" class="form-label">Profile Image</label>
                    <input class="form-control" type="file" id="profileImage" name="ProfileImage" accept="image/*" />
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-success">Create User</button>
                    <a asp-area="Admin" asp-controller="Users" asp-action="Index" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>