﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Helpers;
using AdressBookAppWeb.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace AdressBookAppWeb.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class ProfileController : BaseController
    {
        public ProfileController(AppDbContext context) : base(context)
        {
        }

        // GET: /Admin/Profile
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    return RedirectToAction("Login", "Auth");
                }

                var admin = await _context.Users
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.Id == userId.Value);

                if (admin == null)
                {
                    TempData["ErrorMessage"] = "Unable to load your profile.";
                    return RedirectToAction("Login", "Auth");
                }

                return View(admin);
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while loading your profile.";
                return RedirectToAction("Login", "Auth");
            }
        }

        // POST: /Admin/Profile/UpdateProfile
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateProfile(int id, string email, string firstName, string lastName)
        {
            try
            {
                // Basic validation
                if (string.IsNullOrWhiteSpace(firstName) || string.IsNullOrWhiteSpace(lastName))
                {
                    TempData["ErrorMessage"] = "First Name and Last Name are required.";
                    return RedirectToAction("Index");
                }

                if (string.IsNullOrWhiteSpace(email))
                {
                    TempData["ErrorMessage"] = "Email is required.";
                    return RedirectToAction("Index");
                }

                // Simple email format check
                var emailAttribute = new EmailAddressAttribute();
                if (!emailAttribute.IsValid(email))
                {
                    TempData["ErrorMessage"] = "Please enter a valid email address.";
                    return RedirectToAction("Index");
                }

                var user = await _context.Users.FindAsync(id);
                if (user == null)
                {
                    return NotFound();
                }

                // Check for email uniqueness (if changed)
                if (!string.Equals(user.Email, email, StringComparison.OrdinalIgnoreCase))
                {
                    bool emailTaken = await _context.Users.AnyAsync(u => u.Email == email && u.Id != id);
                    if (emailTaken)
                    {
                        TempData["ErrorMessage"] = "That email is already in use.";
                        return RedirectToAction("Index");
                    }
                }

                user.Email = email.Trim();
                user.FirstName = firstName.Trim();
                user.LastName = lastName.Trim();

                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Profile updated successfully.";
                return RedirectToAction("Index");
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while updating your profile.";
                return RedirectToAction("Index");
            }
        }

        // POST: /Admin/Profile/ChangePassword
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ChangePassword(int id, string currentPassword, string newPassword, string confirmPassword)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(currentPassword) ||
                    string.IsNullOrWhiteSpace(newPassword) ||
                    string.IsNullOrWhiteSpace(confirmPassword))
                {
                    TempData["ErrorMessage"] = "All password fields are required.";
                    return RedirectToAction("Index");
                }

                var user = await _context.Users.FindAsync(id);
                if (user == null)
                {
                    return NotFound();
                }

                if (!PasswordHelper.VerifyPassword(currentPassword, user.Password))
                {
                    TempData["ErrorMessage"] = "Current password is incorrect.";
                    return RedirectToAction("Index");
                }

                if (newPassword != confirmPassword)
                {
                    TempData["ErrorMessage"] = "New password and confirmation do not match.";
                    return RedirectToAction("Index");
                }

                user.Password = PasswordHelper.HashPassword(newPassword);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "Password updated successfully.";
                return RedirectToAction("Index");
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while changing your password.";
                return RedirectToAction("Index");
            }
        }
    }
}