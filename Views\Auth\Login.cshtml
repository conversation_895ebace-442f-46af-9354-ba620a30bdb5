﻿@{
    Layout = "_AuthLayout";
    ViewData["Title"] = "Login";
}

<div class="col-lg-5">
    <div class="card shadow-lg border-0 rounded-lg mt-5">
        <div class="card-header">
            <h3 class="text-center font-weight-light my-4">Login</h3>
        </div>
        <div class="card-body">
            @if (ViewBag.Error != null)
            {
                <div class="alert alert-danger">@ViewBag.Error</div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            @if (TempData["Success"] != null)
            {
                <div class="alert alert-success">@TempData["Success"]</div>
            }

            <form method="post" asp-action="Login">
                <div class="form-floating mb-3">
                    <input name="email" class="form-control" id="inputEmail" type="email" placeholder="<EMAIL>" required />
                    <label for="inputEmail">Email address</label>
                </div>
                <div class="form-floating mb-3">
                    <input name="password" class="form-control" id="inputPassword" type="password" placeholder="Password" required />
                    <label for="inputPassword">Password</label>
                </div>
                <div class="d-flex align-items-center justify-content-between mt-4 mb-0">
                    <a class="small" asp-controller="Auth" asp-action="ResetPass">Forgot Password?</a>
                    <button type="submit" class="btn btn-primary">Login</button>
                </div>
            </form>
        </div>
        <div class="card-footer text-center py-3">
            <div class="small">
                <a asp-controller="Auth" asp-action="Register">Need an account? Sign up!</a>
            </div>
            <div class="small mt-2">
                <a asp-controller="Auth" asp-action="Support">Having trouble logging in? Contact Support</a>
            </div>
        </div>
    </div>
</div>