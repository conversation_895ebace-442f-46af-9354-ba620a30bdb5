﻿@model AdressBookAppWeb.ViewModels.AddContactViewModel

@{
    ViewData["Title"] = "New Contact";
    var groups = ViewBag.Groups as List<AdressBookAppWeb.Models.Group>;
}

<div class="container-fluid px-4 pb-4">
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }
    <form asp-action="AddContact" method="post" enctype="multipart/form-data" class="mt-4">
        <h2>Add Contact Manually</h2>

        <div asp-validation-summary="All" class="text-danger mb-3"></div>

        <div class="mb-3">
            <label asp-for="ContactImage" class="form-label">Upload Contact Image</label>
            <input asp-for="ContactImage" type="file" class="form-control" accept="image/*" onchange="previewImage(event)" />
        </div>

        <div class="mb-3">
            <img id="imagePreview" src="" alt="Image Preview" class="img-thumbnail"
                 style="display: none; max-width: 200px; margin-top: 10px;">
        </div>

        <div class="mb-3">
            <label asp-for="FirstName" class="form-label">First Name*</label>
            <input asp-for="FirstName" class="form-control" placeholder="Enter first name" />
            <span asp-validation-for="FirstName" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="LastName" class="form-label">Last Name</label>
            <input asp-for="LastName" class="form-control" placeholder="Enter last name" />
            <span asp-validation-for="LastName" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Position" class="form-label">Position</label>
            <input asp-for="Position" class="form-control" placeholder="Enter position" />
        </div>

        <div class="mb-3">
            <label asp-for="Email" class="form-label">Email</label>
            <input asp-for="Email" class="form-control" placeholder="Enter email" />
            <span asp-validation-for="Email" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Phone" class="form-label">Phone</label>
            <input asp-for="Phone" class="form-control" placeholder="Enter phone number" />
            <span asp-validation-for="Phone" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Address" class="form-label">Address</label>
            <textarea asp-for="Address" class="form-control" rows="3" placeholder="Enter address"></textarea>
            <span asp-validation-for="Address" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Company" class="form-label">Company</label>
            <input asp-for="Company" class="form-control" placeholder="Enter company name" />
            <span asp-validation-for="Company" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="GroupId" class="form-label">Group</label>
            <select asp-for="GroupId" class="form-control">
                <option value="">Select a group</option>
                @if (groups != null)
                {
                    foreach (var group in groups)
                    {
                        <option value="@group.Id">@group.GroupName</option>
                    }
                }
            </select>
            <span asp-validation-for="GroupId" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Tags" class="form-label">Tags</label>
            <input asp-for="Tags" class="form-control" placeholder="Enter tags (comma separated)" />
            <span asp-validation-for="Tags" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="BirthDate" class="form-label">Birthday</label>
            <input asp-for="BirthDate" type="date" class="form-control" />
            <span asp-validation-for="BirthDate" class="text-danger"></span>
        </div>

        <div class="form-check mb-3">
            <input asp-for="IsStarred" type="checkbox" class="form-check-input" />
            <label asp-for="IsStarred" class="form-check-label">Mark as Starred</label>
        </div>

        <button type="submit" class="btn btn-primary">Add Contact</button>
    </form>
</div>