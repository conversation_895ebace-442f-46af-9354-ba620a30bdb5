﻿@model AdressBookAppWeb.Models.User
@using AdressBookAppWeb.Models.Enums

@{
    ViewData["Title"] = "User Details";
    var currentUser = ViewBag.CurrentUser as AdressBookAppWeb.Models.User;
}

<div class="container-fluid px-4">
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <h1 class="mt-4">User Details</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Dashboard" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Users" asp-action="Index">Users</a></li>
        <li class="breadcrumb-item active">Details</li>
    </ol>

    <div class="card mb-4 shadow-sm">
        <div class="card-body text-center">
            <div class="mb-4">
                <img src="@(string.IsNullOrEmpty(Model.ProfileImage) ? "/assets/img/default.png" : Model.ProfileImage)"
                     class="rounded-circle shadow-sm"
                     alt="Profile Image"
                     width="120"
                     height="120" />
            </div>

            <div class="mb-3">
                <label class="form-label fw-bold">Full Name:</label>
                <div>@Model.FirstName @Model.LastName</div>
            </div>

            <div class="mb-3">
                <label class="form-label fw-bold">Email:</label>
                <div>@Model.Email</div>
            </div>

            <div class="mb-3">
                <label class="form-label fw-bold">Phone:</label>
                <div>@Model.Phone</div>
            </div>

            <div class="mb-3">
                <label class="form-label fw-bold">Role:</label>
                <div>@Model.Role</div>
            </div>

            <div class="mb-3">
                <label class="form-label fw-bold">Registration Date:</label>
                <div>@Model.RegistrationDate.ToString("yyyy-MM-dd")</div>
            </div>

            <div class="d-flex gap-2 justify-content-center mt-4">
                <a asp-area="Admin" asp-controller="Users" asp-action="Index" class="btn btn-secondary">Back to Users</a>

                @if ((currentUser?.Role == UserRole.SuperAdmin) ||
                (currentUser?.Role == UserRole.Admin && Model.Role == UserRole.User))
                {
                    <a asp-area="Admin" asp-controller="Users" asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">Edit User</a>
                }

                @if ((currentUser?.Role == UserRole.SuperAdmin && Model.Role != UserRole.SuperAdmin) ||
                (currentUser?.Role == UserRole.Admin && Model.Role == UserRole.User))
                {
                    <form asp-area="Admin" asp-controller="Users" asp-action="Delete" asp-route-id="@Model.Id" method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this user?');">
                        <button type="submit" class="btn btn-danger">Delete User</button>
                    </form>

                    <form asp-area="Admin" asp-controller="Users" asp-action="ResetPassword" asp-route-id="@Model.Id" method="post" class="d-inline">
                        <button type="submit" class="btn btn-info">Reset Password</button>
                    </form>
                }
            </div>
        </div>
    </div>
</div>