﻿using Microsoft.AspNetCore.Http;
using System;
using System.ComponentModel.DataAnnotations;

namespace AdressBookAppWeb.ViewModels
{
    public class AddContactViewModel
    {
        [Required]
        public string FirstName { get; set; }

        public string? LastName { get; set; }

        public string? Position { get; set; }

        [EmailAddress]
        public string? Email { get; set; }

        public string? Phone { get; set; }

        public string? Address { get; set; }

        public string? Company { get; set; }

        public IFormFile? ContactImage { get; set; }

        public bool IsStarred { get; set; }

        public string? Tags { get; set; }

        [DataType(DataType.Date)]
        public DateTime? BirthDate { get; set; }

        public int? GroupId { get; set; }
    }
}
