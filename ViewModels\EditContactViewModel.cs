﻿using Microsoft.AspNetCore.Http;
using System;
using System.ComponentModel.DataAnnotations;

namespace AdressBookAppWeb.Models.ViewModels
{
    public class EditContactViewModel
    {
        public int Id { get; set; }

        [Required]
        [Display(Name = "First Name")]
        public string FirstName { get; set; }

        [Display(Name = "Last Name")]
        public string? LastName { get; set; }

        public string? Position { get; set; }

        [EmailAddress]
        public string? Email { get; set; }

        public string? Phone { get; set; }

        public string? Address { get; set; }

        public string? Company { get; set; }

        public IFormFile? ContactImage { get; set; }

        public string? ExistingImagePath { get; set; }

        public string? Tags { get; set; }

        public DateTime? BirthDate { get; set; }

        public int? GroupId { get; set; }
    }
}