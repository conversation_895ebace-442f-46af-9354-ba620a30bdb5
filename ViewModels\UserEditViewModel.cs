﻿using System.ComponentModel.DataAnnotations;
using AdressBookAppWeb.Models.Enums;

namespace AdressBookAppWeb.ViewModels
{
    public class UserEditViewModel
    {
        public int Id { get; set; }

        [Required]
        [Display(Name = "First Name")]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Last Name")]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Phone]
        public string? Phone { get; set; }

        public UserRole Role { get; set; }

        public string? ProfileImagePath { get; set; }

        public IFormFile? ProfileImage { get; set; }
    }
}