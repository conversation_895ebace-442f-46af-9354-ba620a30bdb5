﻿@model List<AdressBookAppWeb.Models.Contact>

@{
    ViewData["Title"] = "Dashboard";
    Layout = "~/Views/Shared/_DashboardLayout.cshtml";
}

<div class="container-fluid px-4">

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger mt-4">@TempData["Error"]</div>
    }

    <h2 class="mt-4">Dashboard</h2>

    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-body">
                    <h5 class="card-title mb-3">Contacts</h5>
                </div>
                <div class="card-footer bg-white d-flex align-items-center justify-content-between">
                    <a class="small text-primary stretched-link" asp-controller="Contacts" asp-action="Index">View Details</a>
                    <div class="small text-muted"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-body">
                    <h5 class="card-title mb-3">Groups</h5>
                </div>
                <div class="card-footer bg-white d-flex align-items-center justify-content-between">
                    <a class="small text-primary stretched-link" asp-controller="Groups" asp-action="Index">View Details</a>
                    <div class="small text-muted"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-body">
                    <h5 class="card-title mb-3">Calendar</h5>
                </div>
                <div class="card-footer bg-white d-flex align-items-center justify-content-between">
                    <a class="small text-primary stretched-link" asp-controller="Calendar" asp-action="Index">View Details</a>
                    <div class="small text-muted"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-body">
                    <h5 class="card-title mb-3">Shared Access</h5>
                </div>
                <div class="card-footer bg-white d-flex align-items-center justify-content-between">
                    <a class="small text-primary stretched-link" asp-controller="SharedAccess" asp-action="Index">View Details</a>
                    <div class="small text-muted"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4 shadow-sm">
        <div class="card-header">
            <i class="fas fa-address-book me-1"></i> Recent Contacts
        </div>
        <div class="card-body overflow-auto">
            <table style="min-width: 850px;" class="table table-bordered align-middle">
                <thead>
                    <tr>
                        <th>Image</th>
                        <th>Name</th>
                        <th>Position</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Company</th>
                        <th>Group</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var contact in Model)
                    {
                        <tr>
                            <td class="text-center">
                                <img src="@(!string.IsNullOrEmpty(contact.ContactImage) ? contact.ContactImage : "/assets/img/default.png")"
                                     alt="Profile"
                                     class="rounded-circle"
                                     style="width: 50px; height: 50px; object-fit: cover;" />
                            </td>
                            <td>@contact.Name</td>
                            <td>@contact.Position</td>
                            <td>@contact.Email</td>
                            <td>@contact.Phone</td>
                            <td>@contact.Company</td>
                            <td>@(contact.Group != null ? contact.Group.GroupName : "—")</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>