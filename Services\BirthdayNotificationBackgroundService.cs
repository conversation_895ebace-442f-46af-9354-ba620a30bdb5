﻿using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;
using AdressBookAppWeb.Data;
using Microsoft.EntityFrameworkCore;
using AdressBookAppWeb.Models;

public class BirthdayNotificationBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<BirthdayNotificationBackgroundService> _logger;
    private Timer _timer;

    public BirthdayNotificationBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<BirthdayNotificationBackgroundService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Run at startup and then every 24 hours
        _timer = new Timer(DoWork, null, TimeSpan.Zero, TimeSpan.FromHours(1));
        return Task.CompletedTask;
    }

    private async void DoWork(object state)
    {
        Console.WriteLine("DO BACKGROUND WORK AT " + DateTime.Now);
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var today = DateTime.Today;

            var contactsWithBirthdayToday = await context.Contacts
                .Where(c => c.BirthDate.HasValue &&
                            c.BirthDate.Value.Day == today.Day &&
                            c.BirthDate.Value.Month == today.Month)
                .ToListAsync();

            foreach (var contact in contactsWithBirthdayToday)
            {
                bool alreadyExists = await context.Notifications.AnyAsync(n =>
                    n.ContactId == contact.Id &&
                    n.UserId == contact.UserId &&
                    n.CreatedDate.Date == today);

                if (!alreadyExists)
                {
                    context.Notifications.Add(new Notification
                    {
                        UserId = contact.UserId,
                        ContactId = contact.Id,
                        Message = $"🎂 {contact.Name}'s birthday is today! 🎉",
                        IsRead = false,
                        CreatedDate = DateTime.Now
                    });
                }
            }

            await context.SaveChangesAsync();
            _logger.LogInformation("Birthday notifications generated at {Time}", DateTime.Now);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate birthday notifications.");
        }
    }

    public override void Dispose()
    {
        _timer?.Dispose();
        base.Dispose();
    }
}