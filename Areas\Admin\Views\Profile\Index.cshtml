﻿@model AdressBookAppWeb.Models.User
@{
    ViewData["Title"] = "Admin Profile";
}

<div class="container-fluid px-4">
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success m-4" role="alert">
            @TempData["SuccessMessage"]
        </div>
    }
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger m-4" role="alert">
            @TempData["ErrorMessage"]
        </div>
    }

    <div class="card m-4">
        <div class="card-header">
            <h2>Admin Profile</h2>
        </div>
        <div class="card-body">
            <form asp-action="UpdateProfile" method="post">
                @Html.AntiForgeryToken()
                <input type="hidden" name="id" value="@Model.Id" />

                <div class="mb-3">
                    <p><strong>User Key:</strong> @Model.UserKey</p>
                </div>

                <div class="row mb-3">
                    <div class="col">
                        <label class="form-label">First Name</label>
                        <input type="text" class="form-control" name="firstName" value="@Model.FirstName" required />
                    </div>
                    <div class="col">
                        <label class="form-label">Last Name</label>
                        <input type="text" class="form-control" name="lastName" value="@Model.LastName" required />
                    </div>
                </div>

                <div class="mb-3">
                    <label for="adminEmail" class="form-label">Email</label>
                    <input type="email" class="form-control" id="adminEmail" name="email" value="@Model.Email" required />
                </div>

                <div class="mb-3">
                    <label class="form-label">Role</label>
                    <input type="text" class="form-control" value="@Model.Role" readonly />
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>

    <div class="card m-4">
        <div class="card-header">
            <h2>Change Password</h2>
        </div>
        <div class="card-body">
            <form asp-action="ChangePassword" method="post">
                @Html.AntiForgeryToken()
                <input type="hidden" name="id" value="@Model.Id" />

                <div class="mb-3">
                    <label class="form-label">Current Password</label>
                    <input type="password" class="form-control" name="currentPassword" required />
                </div>
                <div class="mb-3">
                    <label class="form-label">New Password</label>
                    <input type="password" class="form-control" name="newPassword" required />
                </div>
                <div class="mb-3">
                    <label class="form-label">Confirm New Password</label>
                    <input type="password" class="form-control" name="confirmPassword" required />
                </div>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">Update Password</button>
                </div>
            </form>
        </div>
    </div>
</div>