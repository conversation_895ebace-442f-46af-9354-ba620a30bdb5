﻿@{
    ViewData["Title"] = "Import from Excel";
}

<div class="container-fluid px-4">
    <h2 class="mt-4">Import Contacts from Excel</h2>
    <p class="text-muted">Upload an Excel `.xlsx` file to import contacts into your address book.</p>

    @* Success/Error/Warning Alerts *@
    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success">@TempData["Message"]</div>
    }
    @if (TempData["Warning"] != null)
    {
        <div class="alert alert-warning">@TempData["Warning"]</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <div class="row">
        <div class="col-md-8 offset-md-2">
            <form asp-action="ImportFromExcel" method="post" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="excelFile" class="form-label">Select Excel File</label>
                    <input type="file" class="form-control" name="excelFile" accept=".xlsx" required />
                </div>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-success">Upload & Import</button>
                    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
                </div>
            </form>

            <div class="mt-4">
                <h6>📌 Required Excel Columns:</h6>
                <pre class="bg-light p-3 rounded small">
First Name | Last Name | Position | Email | Phone | Address | Company | Tags | BirthDate
                </pre>
            </div>
        </div>
    </div>
</div>