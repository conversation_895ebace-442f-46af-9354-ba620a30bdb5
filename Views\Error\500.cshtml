﻿@{
    ViewData["Title"] = "500 - Internal Server Error";
    Layout = "_ErrorLayout";
    var errorMessage = ViewBag.ErrorMessage as string;
    var stackTrace = ViewBag.StackTrace as string;
}

<div class="text-center mt-5">
    <h1 class="display-4 text-danger">500</h1>
    <p class="lead">An unexpected error occurred on the server.</p>

    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger text-start mt-4">
            <strong>Error:</strong> @errorMessage
            <hr />
            <pre class="text-muted small">@stackTrace</pre>
        </div>
    }
</div>