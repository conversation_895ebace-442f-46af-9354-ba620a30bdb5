﻿@model AdressBookAppWeb.Models.ContactReport
@using AdressBookAppWeb.Models.Enums
@{
    ViewData["Title"] = "Report Details";
}

<div class="container-fluid px-4 py-4">
    <h2 class="mb-4">Report Details</h2>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success">@TempData["Success"]</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <div class="card shadow-sm">
        <div class="card-body">
            <dl class="row mb-0">
                <dt class="col-sm-3">Subject:</dt>
                <dd class="col-sm-9">@Model.Subject</dd>

                <dt class="col-sm-3">Message:</dt>
                <dd class="col-sm-9">@Model.Message</dd>

                <dt class="col-sm-3">Date Submitted:</dt>
                <dd class="col-sm-9">@Model.ReportDate.ToString("yyyy-MM-dd")</dd>

                <dt class="col-sm-3">Status:</dt>
                <dd class="col-sm-9">
                    <span class="badge bg-@GetStatusClass(Model.Status)">
                        @Model.Status
                    </span>
                </dd>
            </dl>
        </div>
    </div>

    <div class="card shadow-sm mt-4">
        <div class="card-header bg-light">
            <strong>Admin Response</strong>
        </div>
        <div class="card-body">
            @if (string.IsNullOrWhiteSpace(Model.AdminResponse))
            {
                <p class="mb-0 text-muted"><em>No response yet.</em></p>
            }
            else
            {
                <p>@Model.AdminResponse</p>
            }
        </div>
    </div>

    <div class="mt-4 d-flex gap-2">
        <a asp-action="Index" class="btn btn-secondary">Back to Profile</a>
        <form method="post" asp-action="CancelReport" asp-route-id="@Model.Id" onsubmit="return confirm('Are you sure you want to cancel this report?');">
            @Html.AntiForgeryToken()
            <button type="submit" class="btn btn-danger">Cancel Report</button>
        </form>
    </div>
</div>

@functions {
    private string GetStatusClass(ReportStatus status) => status switch
    {
        ReportStatus.Pending => "warning text-dark",
        ReportStatus.Reviewed => "success",
        _ => "secondary"
    };
}