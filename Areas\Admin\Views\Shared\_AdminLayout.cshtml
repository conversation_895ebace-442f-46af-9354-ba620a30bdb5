﻿<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <link href="https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/style.min.css" rel="stylesheet" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <link href="~/css/styles.css" rel="stylesheet" />
    <script src="https://use.fontawesome.com/releases/v6.3.0/js/all.js" crossorigin="anonymous"></script>
    <title>@ViewBag.Title</title>
</head>
<body class="sb-nav-fixed">
    <nav class="sb-topnav navbar navbar-expand navbar-dark bg-dark">
        <a class="navbar-brand ps-3" asp-controller="Dashboard" asp-action="Index">Address Book</a>
        <button class="btn btn-link btn-sm order-1 order-lg-0 me-4 me-lg-0" id="sidebarToggle" href="#!">
            <i class="fas fa-bars"></i>
        </button>
        <ul class="navbar-nav ms-auto">
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" id="navbarDropdown" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-user fa-fw"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                    <li><a asp-controller="Profile" asp-action="Index" class="dropdown-item">Profile</a></li>
                    <li><hr class="dropdown-divider" /></li>
                    <li><a class="dropdown-item" asp-controller="Auth" asp-area="" asp-action="Index">Logout</a></li>
                </ul>
            </li>
        </ul>
    </nav>
    <div id="layoutSidenav">
        <div id="layoutSidenav_nav">
            <nav class="sb-sidenav accordion sb-sidenav-dark" id="sidenavAccordion">
                <div class="sb-sidenav-menu">
                    <div class="nav">
                        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Dashboard" ? "active" : "")"
                           asp-area="Admin" asp-controller="Dashboard" asp-action="Index">
                            <div class="sb-nav-link-icon"><i class="fas fa-tachometer-alt"></i></div>
                            Dashboard
                        </a>

                        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Users" ? "active" : "")"
                           asp-area="Admin" asp-controller="Users" asp-action="Index">
                            <div class="sb-nav-link-icon"><i class="fas fa-users"></i></div>
                            Users
                        </a>

                        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "ContactReports" ? "active" : "")"
                           asp-area="Admin" asp-controller="ContactReports" asp-action="Index">
                            <div class="sb-nav-link-icon"><i class="fas fa-envelope"></i></div>
                            Contact Reports
                        </a>

                        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Logs" ? "active" : "")"
                           asp-area="Admin" asp-controller="Logs" asp-action="Index">
                            <div class="sb-nav-link-icon"><i class="fas fa-clipboard-list"></i></div>
                            Logs
                        </a>
                    </div>
                </div>
                <div class="sb-sidenav-footer">
                    <div class="small">Logged in as:</div>
                    @if (ViewBag.CurrentUser != null)
                    {
                        <text>@ViewBag.CurrentUser.FirstName @ViewBag.CurrentUser.LastName</text>
                    }
                    else
                    {
                        Console.WriteLine(ViewBag);
                        <text>Guest</text>
                    }
                </div>
            </nav>
        </div>
        <div id="layoutSidenav_content">
            <main>
                @RenderBody()
            </main>
        </div>
    </div>


    @* <!-- calendar start -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.6/main.min.css">
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.6/index.global.min.js"></script>
    <!-- calendar end --> *@

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"
            crossorigin="anonymous"></script>
    <script src="~/js/scripts.js"></script>
    @* <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.min.js" crossorigin="anonymous"></script> *@
    @* <script src="~/assets/demo/chart-area-demo.js"></script> *@
    @* <script src="~/assets/demo/chart-bar-demo.js"></script> *@
    @* <script src="https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/umd/simple-datatables.min.js" *@
    @*         crossorigin="anonymous"></script> *@
    @* <script src="~/js/datatables-simple-demo.js"></script> *@
</body>
</html>
