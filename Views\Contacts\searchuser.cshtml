﻿@using AdressBookAppWeb.Models
@{
    ViewData["Title"] = "Search User";
    var groups = ViewBag.Groups as List<Group>;
}

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center my-4">
        <h1 class="h3">Add contact or give access using User Key</h1>
    </div>

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }
    @if (TempData["Message"] != null)
    {
        <div class="alert alert-info">@TempData["Message"]</div>
    }

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form id="searchUserForm" method="post">
                <div class="input-group mb-3">
                    <input type="text" class="form-control" id="userKey" name="userKey" placeholder="Enter User's User Key" required>
                    <button class="btn btn-primary" type="submit" id="searchButton">Search</button>
                </div>
                @if (ViewBag.Error != null)
                {
                    <div class="text-danger">@ViewBag.Error</div>
                }
            </form>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <h5 class="card-title">Search Results</h5>
            <div id="searchResults" class="mt-3">
                @if (ViewBag.FoundUser != null)
                {
                    var user = (AdressBookAppWeb.Models.User)ViewBag.FoundUser;
                    <div class="card mb-3">
                        <div class="card-body d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1">@user.FirstName @user.LastName</h5>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary action-user-button"
                                        data-user-id="@user.Id"
                                        data-user-name="@user.FirstName @user.LastName"
                                        data-bs-toggle="modal"
                                        data-bs-target="#userActionModal">
                                    Actions
                                </button>
                            </div>
                        </div>
                    </div>
                }
                else if (TempData["Message"] == null && TempData["Error"] == null)
                {
                    <p class="text-muted">No user searched yet.</p>
                }
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="userActionModal" tabindex="-1" aria-labelledby="userActionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title" id="userActionModalLabel">Choose action for <span id="selectedUserName"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success" id="addContactBtn">Add this user as contact to your contacts list</button>
                    <button class="btn btn-warning" id="giveAccessBtn">Give access to look your contacts</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">Add Contact to Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Adding <strong id="selectedUserName"></strong> to a group.</p>
                <form id="addUserForm" method="post" action="/Contacts/AddUserAsContact">
                    <input type="hidden" name="userId" id="userId">
                    <div class="mb-3">
                        <label for="groupSelect" class="form-label">Select Group:</label>
                        <select class="form-select" name="groupId" id="groupSelect" required>
                            <option value="">Select Group</option>
                            <option value="0">No Group</option>
                            @foreach (var group in groups ?? new List<Group>())
                            {
                                <option value="@group.Id">@group.GroupName</option>
                            }
                        </select>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('.action-user-button').forEach(button => {
                button.addEventListener('click', () => {
                    const userId = button.getAttribute('data-user-id');
                    const userName = button.getAttribute('data-user-name');

                    document.getElementById('userId').value = userId;
                    document.querySelectorAll('#selectedUserName').forEach(el => el.innerText = userName);

                    document.getElementById('addContactBtn').onclick = () => {
                        var modal = new bootstrap.Modal(document.getElementById('addUserModal'));
                        modal.show();
                    };

                    document.getElementById('giveAccessBtn').onclick = () => {
                        const form = document.createElement('form');
                        form.method = 'post';
                        form.action = '/Contacts/GrantAccess';

                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'userId';
                        input.value = userId;

                        form.appendChild(input);
                        document.body.appendChild(form);
                        form.submit();
                    };
                });
            });
        });
    </script>
}