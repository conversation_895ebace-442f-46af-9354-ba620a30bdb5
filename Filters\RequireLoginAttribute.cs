﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models.Enums;

public class RequireLoginAttribute : ActionFilterAttribute
{
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        var httpContext = context.HttpContext;
        var session = httpContext.Session;
        var path = httpContext.Request.Path;

        bool isPublic = path.StartsWithSegments("/Auth", StringComparison.OrdinalIgnoreCase);

        var userId = session.GetInt32("UserId");

        if (userId == null && !isPublic)
        {
            if (context.Controller is Controller controller)
            {
                var isSessionExisting = httpContext.Request.Cookies.ContainsKey(".AspNetCore.Session");

                controller.TempData["ErrorMessage"] = isSessionExisting
                    ? "Your session has expired. Please log in again."
                    : "You must log in to access this page.";
            }

            context.Result = new RedirectToActionResult("Login", "Auth", new { area = "" });
            return;
        }

        var area = context.RouteData.Values["area"]?.ToString();
        if (area?.Equals("Admin", StringComparison.OrdinalIgnoreCase) == true)
        {
            var db = httpContext.RequestServices.GetService(typeof(AppDbContext)) as AppDbContext;
            var user = db?.Users.FirstOrDefault(u => u.Id == userId);

            if (user == null || (user.Role != UserRole.Admin && user.Role != UserRole.SuperAdmin))
            {
                if (context.Controller is Controller controller)
                {
                    controller.TempData["ErrorMessage"] = "You must be an admin to access this page.";
                }

                context.Result = new RedirectToActionResult("Login", "Auth", new { area = "" });
                return;
            }
        }

        base.OnActionExecuting(context);
    }
}