﻿@model IEnumerable<AdressBookAppWeb.Models.ContactReport>
@using AdressBookAppWeb.Models.Enums

@{
    ViewData["Title"] = "Contact Reports";
    var filters = ViewBag.Filters;
    var page = ViewBag.CurrentPage as int? ?? 1;
    var totalPages = ViewBag.TotalPages as int? ?? 1;
}

<div class="container-fluid px-4">
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <h1 class="mt-4">Contact Reports</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item">
            <a asp-controller="Dashboard" asp-action="Index">Dashboard</a>
        </li>
        <li class="breadcrumb-item active">Contact Reports</li>
    </ol>

    <div class="d-flex justify-content-end mb-3">
        <button class="btn btn-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#filterOffcanvas">
            <i class="fas fa-filter"></i> Filter Reports
        </button>
    </div>

    <div class="offcanvas offcanvas-end" tabindex="-1" id="filterOffcanvas">
        <div class="offcanvas-header">
            <h5>Filter Contact Reports</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <form method="get">
                <div class="mb-3">
                    <label class="form-label">Sender Name</label>
                    <input class="form-control" name="sender" value="@filters?.sender" />
                </div>
                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <input class="form-control" name="email" value="@filters?.email" />
                </div>
                <div class="mb-3">
                    <label class="form-label">Subject</label>
                    <input class="form-control" name="subject" value="@filters?.subject" />
                </div>
                <div class="mb-3">
                    <label class="form-label">Status</label>
                    <select class="form-select" name="status">
                        <option value="">All Statuses</option>
                        @foreach (ReportStatus s in Enum.GetValues(typeof(ReportStatus)))
                        {
                            var isSelected = (filters?.status == s.ToString());
                            <option value="@s" selected="@(isSelected ? "selected" : null)">@s</option>
                        }
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Date</label>
                    <input type="date" class="form-control" name="date" value="@(filters?.date != null ? ((DateTime)filters.date).ToString("yyyy-MM-dd") : "")" />
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" type="submit">Apply</button>
                    <a asp-action="Index" class="btn btn-secondary">Reset</a>
                </div>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-envelope me-1"></i>
            Incoming Contact Us Messages
        </div>
        <div class="card-body">
            <table class="table table-bordered table-striped align-middle">
                <thead>
                    <tr>
                        <th style="width:50px;">#</th>
                        <th>Sender</th>
                        <th>Email</th>
                        <th>Subject</th>
                        <th>Message</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th style="width:150px;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var report in Model)
                    {
                        <tr>
                            <td>@report.Id</td>
                            <td>@report.SenderName</td>
                            <td>@report.Email</td>
                            <td>@report.Subject</td>
                            <td>
                                @{
                                    var msg = report.Message ?? "";
                                    @((msg.Length > 60) ? msg.Substring(0, 60) + "..." : msg)
                                }
                            </td>
                            <td>
                                @if (report.Status == ReportStatus.Pending)
                                {
                                    <span class="badge bg-warning text-dark">Pending</span>
                                }
                                else if (report.Status == ReportStatus.Reviewed)
                                {
                                    <span class="badge bg-success">Reviewed</span>
                                }
                            </td>
                            <td>@report.ReportDate.ToString("yyyy-MM-dd")</td>
                            <td>
                                <div class="d-flex gap-1 flex-wrap">
                                    <a asp-area="Admin"
                                       asp-controller="ContactReports"
                                       asp-action="Detail"
                                       asp-route-id="@report.Id"
                                       class="btn btn-sm btn-info">
                                        View
                                    </a>
                                    <form asp-area="Admin"
                                          asp-controller="ContactReports"
                                          asp-action="Delete"
                                          asp-route-id="@report.Id"
                                          method="post"
                                          onsubmit="return confirm('Are you sure you want to delete this report?');"
                                          class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-3">
                <nav>
                    <ul class="pagination">
                        <li class="page-item @(page <= 1 ? "disabled" : "")">
                            <a class="page-link" href="?page=@(page - 1)">Previous</a>
                        </li>
                        @for (int i = 1; i <= totalPages; i++)
                        {
                            <li class="page-item @(i == page ? "active" : "")">
                                <a class="page-link" href="?page=@i">@i</a>
                            </li>
                        }
                        <li class="page-item @(page >= totalPages ? "disabled" : "")">
                            <a class="page-link" href="?page=@(page + 1)">Next</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>