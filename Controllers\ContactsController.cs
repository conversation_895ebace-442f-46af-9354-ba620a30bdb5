﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using AdressBookAppWeb.Models.ViewModels;
using AdressBookAppWeb.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace AdressBookAppWeb.Controllers
{
    public class ContactsController : BaseController
    {
        public ContactsController(AppDbContext context) : base(context)
        {
        }

        [HttpGet]
        public IActionResult Index(
            string? fullnameQuery,
            string? email,
            string? phoneQuery,
            string? group,
            int? groupId,
            DateTime? birthdayFrom,
            DateTime? birthdayTo,
            bool? starred,
            bool? recentlyAdded,
            int page = 1,
            int pageSize = 9)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                    return RedirectToAction("Login", "Auth");

                var query = _context.Contacts
                    .Include(c => c.Group)
                    .Where(c => c.UserId == userId.Value)
                    .AsQueryable();

                if (!string.IsNullOrWhiteSpace(fullnameQuery))
                {
                    query = query.Where(c =>
                        c.FirstName.Contains(fullnameQuery) ||
                        (c.LastName != null && c.LastName.Contains(fullnameQuery)));
                }

                if (!string.IsNullOrWhiteSpace(email))
                    query = query.Where(c => c.Email.Contains(email));

                if (!string.IsNullOrWhiteSpace(phoneQuery))
                    query = query.Where(c => c.Phone.Contains(phoneQuery));

                if (!string.IsNullOrWhiteSpace(group))
                    query = query.Where(c => c.Group != null && c.Group.GroupName == group);

                if (groupId.HasValue)
                    query = query.Where(c => c.GroupId == groupId);

                if (birthdayFrom.HasValue)
                    query = query.Where(c => c.BirthDate >= birthdayFrom.Value);

                if (birthdayTo.HasValue)
                    query = query.Where(c => c.BirthDate <= birthdayTo.Value);

                if (starred == true)
                    query = query.Where(c => c.IsStarred);

                if (recentlyAdded == true)
                    query = query.OrderByDescending(c => c.CreatedDate);

                int totalItems = query.Count();
                int totalPages = (int)Math.Ceiling(totalItems / (double)pageSize);

                var contacts = query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                ViewBag.Contacts = contacts;
                ViewBag.Groups = _context.Groups
                    .Where(g => g.UserId == userId.Value)
                    .ToList();
                ViewBag.CurrentPage = page;
                ViewBag.TotalPages = totalPages;
                ViewBag.QueryParams = new Dictionary<string, string?>
                {
                    ["fullnameQuery"] = fullnameQuery,
                    ["email"] = email,
                    ["phoneQuery"] = phoneQuery,
                    ["group"] = group,
                    ["groupId"] = groupId?.ToString(),
                    ["birthdayFrom"] = birthdayFrom?.ToString("yyyy-MM-dd"),
                    ["birthdayTo"] = birthdayTo?.ToString("yyyy-MM-dd"),
                    ["starred"] = starred?.ToString().ToLower(),
                    ["recentlyAdded"] = recentlyAdded?.ToString().ToLower()
                };

                return View();
            }
            catch (Exception ex)
            {
                // Log and surface error
                LogActivity(
                    LogAction.ViewContacts,
                    $"Exception in Index: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                ViewBag.Error = "An error occurred while loading contacts.";
                return View(new { });
            }
        }

        [HttpGet]
        public IActionResult AddContact()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                    return RedirectToAction("Login", "Auth");

                ViewBag.Groups = _context.Groups
                    .Where(g => g.UserId == userId.Value)
                    .ToList();
                return View(new AddContactViewModel());
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.AddContact,
                    $"Exception in AddContact GET: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "Unable to load page. Please try again.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        public IActionResult AddContact(AddContactViewModel model)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
                return RedirectToAction("Login", "Auth");

            try
            {
                if (!string.IsNullOrWhiteSpace(model.Phone) &&
                    _context.Contacts.Any(c => c.UserId == userId && c.Phone == model.Phone))
                {
                    ModelState.AddModelError("Phone", "Phone number is already in use.");
                }

                if (!ModelState.IsValid)
                {
                    ViewBag.Groups = _context.Groups
                        .Where(g => g.UserId == userId.Value)
                        .ToList();
                    return View(model);
                }

                string imagePath = "/assets/img/default.png";
                if (model.ContactImage != null && model.ContactImage.Length > 0)
                {
                    var uploadsDir = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot/uploads");
                    Directory.CreateDirectory(uploadsDir);

                    var fileName = $"{Guid.NewGuid()}_{Path.GetFileName(model.ContactImage.FileName)}";
                    var filePath = Path.Combine(uploadsDir, fileName);
                    using var stream = new FileStream(filePath, FileMode.Create);
                    model.ContactImage.CopyTo(stream);

                    imagePath = "/uploads/" + fileName;
                }

                var contact = new Contact
                {
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    Position = model.Position,
                    Email = model.Email,
                    Phone = model.Phone,
                    Address = model.Address,
                    Company = model.Company,
                    ContactImage = imagePath,
                    IsStarred = model.IsStarred,
                    Tags = model.Tags,
                    BirthDate = model.BirthDate,
                    GroupId = model.GroupId,
                    CreatedDate = DateTime.Now,
                    UserId = userId.Value
                };

                _context.Contacts.Add(contact);
                _context.SaveChanges();

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.AddContact,
                    $"Exception in AddContact POST: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                ViewBag.Error = "Failed to add contact. Please try again.";
                ViewBag.Groups = _context.Groups
                    .Where(g => g.UserId == userId.Value)
                    .ToList();
                return View(model);
            }
        }

        [HttpPost]
        public IActionResult Delete(int id)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
                return RedirectToAction("Login", "Auth");

            try
            {
                var contact = _context.Contacts
                    .FirstOrDefault(c => c.Id == id && c.UserId == userId.Value);

                if (contact != null)
                {
                    _context.Contacts.Remove(contact);
                    _context.SaveChanges();
                }
                else
                {
                    TempData["Error"] = "Contact not found.";
                }

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.DeleteContact,
                    $"Exception in Delete: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "An error occurred while deleting the contact.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        public IActionResult BulkDelete([FromBody] List<int> selectedContacts)
        {
            var currentUserId = HttpContext.Session.GetInt32("UserId");
            if (currentUserId == null)
                return RedirectToAction("Login", "Auth");

            try
            {
                if (selectedContacts == null || !selectedContacts.Any())
                {
                    LogActivity(
                        LogAction.BulkDelete,
                        "Bulk delete attempted with no contacts selected.",
                        LogStatus.Error,
                        LogModule.Contacts);
                    return BadRequest("No contacts selected.");
                }

                var contacts = _context.Contacts
                    .Where(c => selectedContacts.Contains(c.Id) && c.UserId == currentUserId.Value)
                    .ToList();

                if (!contacts.Any())
                {
                    return BadRequest("No matching contacts to delete.");
                }

                _context.Contacts.RemoveRange(contacts);
                _context.SaveChanges();

                LogActivity(
                    LogAction.BulkDelete,
                    $"Bulk deleted {contacts.Count} contacts.",
                    LogStatus.Successful,
                    LogModule.Contacts);

                return Ok();
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.BulkDelete,
                    $"Exception during bulk delete: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                return BadRequest("An error occurred while deleting contacts.");
            }
        }

        [HttpPost]
        public IActionResult ToggleStar(int id)
        {
            try
            {
                var contact = _context.Contacts.FirstOrDefault(c => c.Id == id);
                if (contact == null)
                    return NotFound();

                contact.IsStarred = !contact.IsStarred;
                contact.UpdatedDate = DateTime.Now;
                _context.SaveChanges();

                return Ok();
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ToggleStar,
                    $"Exception in ToggleStar: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                return BadRequest("Failed to toggle star status.");
            }
        }

        [HttpGet]
        public IActionResult EditContact(int id)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                    return RedirectToAction("Login", "Auth");

                var contact = _context.Contacts
                    .FirstOrDefault(c => c.Id == id && c.UserId == userId.Value);

                if (contact == null)
                    return NotFound();

                var vm = new EditContactViewModel
                {
                    Id = contact.Id,
                    FirstName = contact.FirstName,
                    LastName = contact.LastName,
                    Position = contact.Position,
                    Email = contact.Email,
                    Phone = contact.Phone,
                    Address = contact.Address,
                    Company = contact.Company,
                    Tags = contact.Tags,
                    BirthDate = contact.BirthDate,
                    GroupId = contact.GroupId,
                    ExistingImagePath = contact.ContactImage
                };

                ViewBag.Groups = _context.Groups
                    .Where(g => g.UserId == userId.Value)
                    .ToList();
                return View(vm);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.EditContact,
                    $"Exception in EditContact GET: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "Unable to load contact for editing.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        public IActionResult EditContact(int id, EditContactViewModel model)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
                return RedirectToAction("Login", "Auth");

            try
            {
                var contact = _context.Contacts
                    .FirstOrDefault(c => c.Id == id && c.UserId == userId.Value);
                if (contact == null)
                    return NotFound();

                // Check phone uniqueness
                if (!string.IsNullOrWhiteSpace(model.Phone) &&
                    _context.Contacts.Any(c => c.UserId == userId && c.Phone == model.Phone && c.Id != id))
                {
                    ModelState.AddModelError("Phone", "Phone number is already in use.");
                }

                if (!ModelState.IsValid)
                {
                    ViewBag.Groups = _context.Groups
                        .Where(g => g.UserId == userId.Value)
                        .ToList();
                    return View(model);
                }

                if (model.ContactImage != null && model.ContactImage.Length > 0)
                {
                    var fileName = $"{Guid.NewGuid()}_{model.ContactImage.FileName}";
                    var uploadPath = Path.Combine("wwwroot/uploads", fileName);
                    Directory.CreateDirectory(Path.GetDirectoryName(uploadPath)!);
                    using var stream = new FileStream(uploadPath, FileMode.Create);
                    model.ContactImage.CopyTo(stream);
                    contact.ContactImage = "/uploads/" + fileName;
                }

                contact.FirstName = model.FirstName;
                contact.LastName = model.LastName;
                contact.Position = model.Position;
                contact.Email = model.Email;
                contact.Phone = model.Phone;
                contact.Address = model.Address;
                contact.Company = model.Company;
                contact.Tags = model.Tags;
                contact.BirthDate = model.BirthDate;
                contact.GroupId = model.GroupId;
                contact.UpdatedDate = DateTime.Now;

                _context.SaveChanges();
                return RedirectToAction("ContactDetail", new { id = contact.Id });
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.EditContact,
                    $"Exception in EditContact POST: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                ViewBag.Error = "Failed to update contact. Please try again.";
                ViewBag.Groups = _context.Groups
                    .Where(g => g.UserId == userId.Value)
                    .ToList();
                return View(model);
            }
        }

        [HttpGet]
        public IActionResult ContactDetail(int id)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                    return RedirectToAction("Login", "Auth");

                var contact = _context.Contacts
                    .Include(c => c.Group)
                    .FirstOrDefault(c => c.Id == id && c.UserId == userId.Value);

                if (contact == null)
                    return NotFound();

                return View(contact);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ViewContactDetail,
                    $"Exception in ContactDetail: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "Unable to load contact details.";
                return RedirectToAction("Index");
            }
        }

        [HttpGet]
        public IActionResult SearchUser()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                    return RedirectToAction("Login", "Auth");

                ViewBag.Groups = _context.Groups
                    .Where(g => g.UserId == userId.Value)
                    .ToList();
                return View();
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.SearchUser,
                    $"Exception in SearchUser GET: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "Unable to load search page.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        public IActionResult SearchUser(string userKey)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                    return RedirectToAction("Login", "Auth");

                ViewBag.Groups = _context.Groups
                    .Where(g => g.UserId == userId.Value)
                    .ToList();

                if (string.IsNullOrWhiteSpace(userKey))
                {
                    ViewBag.Error = "User key cannot be empty.";
                    return View();
                }

                var foundUser = _context.Users
                    .FirstOrDefault(u => u.UserKey == userKey && u.Id != userId.Value);
                if (foundUser == null)
                {
                    ViewBag.Error = "User not found or you're trying to search yourself.";
                    return View();
                }

                ViewBag.FoundUser = foundUser;
                return View();
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.SearchUser,
                    $"Exception in SearchUser POST: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                ViewBag.Error = "An error occurred while searching.";
                ViewBag.Groups = _context.Groups
                    .Where(g => g.UserId == HttpContext.Session.GetInt32("UserId")!.Value)
                    .ToList();
                return View();
            }
        }

        [HttpPost]
        public IActionResult AddUserAsContact(int userId, int groupId)
        {
            var currentUserId = HttpContext.Session.GetInt32("UserId");
            if (currentUserId == null)
                return RedirectToAction("Login", "Auth");

            try
            {
                var user = _context.Users.Find(userId);
                if (user == null)
                {
                    LogActivity(
                        LogAction.AddContactUserKey,
                        $"Add contact failed—user id {userId} not found.",
                        LogStatus.Error,
                        LogModule.Contacts,
                        currentUserId.Value);
                    return NotFound();
                }

                bool emailExists = _context.Contacts
                    .Any(c => c.UserId == currentUserId.Value && c.Email == user.Email);

                bool phoneExists = !string.IsNullOrWhiteSpace(user.Phone) &&
                    _context.Contacts.Any(c => c.UserId == currentUserId.Value && c.Phone == user.Phone);

                if (emailExists || phoneExists)
                {
                    string conflictInfo = emailExists && phoneExists
                        ? "email and phone"
                        : emailExists ? "email" : "phone";

                    LogActivity(
                        LogAction.AddContactUserKey,
                        $"User {user.Email} already exists in contacts (duplicate {conflictInfo}).",
                        LogStatus.Error,
                        LogModule.Contacts,
                        currentUserId.Value);

                    TempData["Message"] = $"User already exists in your contacts (duplicate {conflictInfo}).";
                    return RedirectToAction("SearchUser");
                }

                var contact = new Contact
                {
                    UserId = currentUserId.Value,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Email = user.Email,
                    Phone = user.Phone,
                    ContactImage = user.ProfileImage,
                    GroupId = groupId == 0 ? null : groupId,
                    IsStarred = false,
                    CreatedDate = DateTime.UtcNow
                };

                _context.Contacts.Add(contact);
                _context.SaveChanges();

                LogActivity(
                    LogAction.AddContactUserKey,
                    $"Added user {user.Email} as contact (group {groupId}).",
                    LogStatus.Successful,
                    LogModule.Contacts,
                    currentUserId.Value);

                TempData["Message"] = "Contact added successfully.";
                return RedirectToAction("SearchUser");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.AddContactUserKey,
                    $"Exception adding user as contact: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts,
                    currentUserId.Value);

                TempData["Error"] = "An error occurred while adding contact.";
                return RedirectToAction("SearchUser");
            }
        }

        [HttpPost]
        public IActionResult GrantAccess(int userId)
        {
            var currentUserId = HttpContext.Session.GetInt32("UserId");
            if (currentUserId == null)
                return RedirectToAction("Login", "Auth");

            try
            {
                var existing = _context.SharedAccesses
                    .FirstOrDefault(a => a.OwnerId == currentUserId && a.ViewerId == userId);

                if (existing == null)
                {
                    var access = new SharedAccess
                    {
                        OwnerId = currentUserId.Value,
                        ViewerId = userId,
                        AccessStatus = AccessStatus.Accessed,
                        GrantedDate = DateTime.UtcNow
                    };

                    _context.SharedAccesses.Add(access);
                    _context.SaveChanges();
                    TempData["Message"] = "Access granted.";
                }
                else
                {
                    TempData["Message"] = "User already has access.";
                }

                return RedirectToAction("SearchUser");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.GrantAccess,
                    $"Exception in GrantAccess: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts,
                    currentUserId.Value);

                TempData["Error"] = "Failed to grant access. Please try again.";
                return RedirectToAction("SearchUser");
            }
        }
    }
}