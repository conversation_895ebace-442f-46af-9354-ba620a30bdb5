﻿@model List<AdressBookAppWeb.Models.Group>
@{
    ViewData["Title"] = "Groups";
}

<div class="container-fluid px-4 mt-4">
    <h2 class="mb-4">Groups Management</h2>

    @* Alerts *@
    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success">@TempData["Message"]</div>
    }
    @if (TempData["Warning"] != null)
    {
        <div class="alert alert-warning">@TempData["Warning"]</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <a class="btn btn-primary" asp-action="AddGroup">+ Create New Group</a>
            <button class="btn btn-secondary" data-bs-toggle="offcanvas" data-bs-target="#groupFilterOffcanvas">
                Filter Groups
            </button>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th style="width: 40px;"><input type="checkbox" id="selectAllGroups" /></th>
                        <th>Group Name</th>
                        <th>Group Description</th>
                        <th style="width: 120px;">Total Contacts</th>
                        <th style="width: 200px;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model.Any())
                    {
                        foreach (var group in Model)
                        {
                            <tr>
                                <td>
                                    <input type="checkbox" class="group-checkbox" value="@group.Id" />
                                </td>
                                <td>@group.GroupName</td>
                                <td>@group.GroupDescription</td>
                                <td>@(group.Contacts?.Count ?? 0)</td>
                                <td>
                                    <a asp-controller="Contacts"
                                       asp-action="Index"
                                       asp-route-groupId="@group.Id"
                                       class="btn btn-sm btn-info me-2">
                                        View Contacts
                                    </a>
                                    <a asp-action="EditGroup"
                                       asp-route-id="@group.Id"
                                       class="btn btn-sm btn-warning me-2">
                                        Edit
                                    </a>
                                    <button class="btn btn-sm btn-danger delete-group" data-id="@group.Id">
                                        Delete
                                    </button>
                                </td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td colspan="5" class="text-center text-muted">No groups found.</td>
                        </tr>
                    }
                </tbody>
            </table>

            <div class="d-flex justify-content-end mt-3">
                <button class="btn btn-danger" id="deleteSelected">Delete Selected</button>
            </div>
        </div>
    </div>
</div>

<div class="offcanvas offcanvas-end" tabindex="-1" id="groupFilterOffcanvas" aria-labelledby="groupFilterOffcanvasLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="groupFilterOffcanvasLabel">Filter Groups</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <form method="post" asp-action="Filter">
            @Html.AntiForgeryToken()

            <div class="mb-3">
                <label class="form-label">Group Name</label>
                <input type="text" name="groupName" class="form-control"
                       value="@(Context.Request.Query["groupName"])" />
            </div>

            <div class="mb-3">
                <label class="form-label">Description</label>
                <input type="text" name="description" class="form-control"
                       value="@(Context.Request.Query["description"])" />
            </div>

            <div class="mb-3">
                <label class="form-label">Minimum Contacts</label>
                <input type="number" name="minContacts" class="form-control" min="0"
                       value="@(Context.Request.Query["minContacts"])" />
            </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">Search</button>
                <a href="/Groups" class="btn btn-secondary">Reset</a>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const selectAll = document.getElementById("selectAllGroups");
        const checkboxes = document.querySelectorAll(".group-checkbox");
        const token = document.querySelector('input[name="__RequestVerificationToken"]').value;

        if (selectAll) {
            selectAll.addEventListener("change", function () {
                checkboxes.forEach(cb => cb.checked = selectAll.checked);
            });
        }

        document.querySelectorAll(".delete-group").forEach(btn => {
            btn.addEventListener("click", function () {
                const id = this.dataset.id;
                if (confirm("Are you sure you want to delete this group?")) {
                    fetch(`/Groups/DeleteGroup`, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        },
                        body: JSON.stringify({ id: parseInt(id) })
                    })
                    .then(res => res.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.message || "Failed to delete group.");
                        }
                    });
                }
            });
        });

        document.getElementById("deleteSelected").addEventListener("click", function () {
            const selectedIds = Array.from(document.querySelectorAll(".group-checkbox:checked"))
                .map(cb => parseInt(cb.value));

            if (selectedIds.length === 0) {
                alert("Please select at least one group to delete.");
                return;
            }

            if (confirm("Are you sure you want to delete selected groups?")) {
                fetch(`/Groups/DeleteSelectedGroups`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "RequestVerificationToken": token
                    },
                    body: JSON.stringify(selectedIds)
                })
                .then(res => res.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || "Failed to delete selected groups.");
                    }
                });
            }
        });
    });
</script>