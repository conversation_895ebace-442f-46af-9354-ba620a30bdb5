﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace AdressBookAppWeb.Controllers
{
    public class DashboardController : BaseController
    {
        public DashboardController(AppDbContext context) : base(context)
        {
        }

        [HttpGet]
        public IActionResult Index()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to view the dashboard.";
                    return RedirectToAction("Login", "Auth");
                }

                var recentContacts = _context.Contacts
                    .AsNoTracking()
                    .Include(c => c.Group)
                    .Where(c => c.UserId == userId.Value)
                    .OrderByDescending(c => c.CreatedDate)
                    .Take(5)
                    .ToList();

                return View(recentContacts);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ViewDashboard,
                    $"Exception in Dashboard/Index: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Dashboard);

                TempData["Error"] = "An error occurred while loading the dashboard.";
                return View(new List<Contact>());
            }
        }
    }
}