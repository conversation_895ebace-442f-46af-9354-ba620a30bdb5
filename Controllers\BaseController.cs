﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;

namespace AdressBookAppWeb.Controllers
{
    public class BaseController : Controller
    {
        protected readonly AppDbContext _context;

        public BaseController(AppDbContext context)
        {
            _context = context;
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId.HasValue)
                {
                    // Fetch current user
                    var user = _context.Users.FirstOrDefault(u => u.Id == userId.Value);
                    ViewBag.CurrentUser = user;

                    // Fetch unread notifications
                    var unreadNotifications = _context.Notifications
                        .Include(n => n.Contact)
                        .Where(n => n.UserId == userId.Value && !n.IsRead)
                        .OrderByDescending(n => n.CreatedDate)
                        .ToList();

                    ViewBag.UnreadNotificationCount = unreadNotifications.Count;
                    ViewBag.UnreadNotificationsTop3 = unreadNotifications
                        .Take(3)
                        .Select(n => new
                        {
                            n.Id,
                            ContactName = n.Contact?.Name,
                            Message = $"🎂 {n.Contact?.Name}'s birthday is today! 🎉"
                        })
                        .ToList();
                }
            }
            catch (Exception)
            {
                ViewBag.Error= "Unable to load notifications or UserData. You may miss some alerts.";
                TempData["Error"] = "Unable to load notifications or UserData. You may miss some alerts.";
            }

            base.OnActionExecuting(context);
        }

        protected void LogActivity(LogAction action, string message, LogStatus status, LogModule module, int? userId = null)
        {
            try
            {
                var log = new Log
                {
                    Action = action,
                    Module = module,
                    Message = message,
                    Status = status,
                    Timestamp = DateTime.Now,
                    IPAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "N/A",
                    UserId = userId ?? HttpContext.Session.GetInt32("UserId")
                };

                _context.Logs.Add(log);
                _context.SaveChanges();
            }
            catch
            {
                // Keep silent; we do not want logging to break the app
            }
        }
    }
}