﻿@model AdressBookAppWeb.ViewModels.NotificationListViewModel

@{
    ViewData["Title"] = "Notifications";
}

<div class="container-fluid px-4 py-4">
    <h2 class="mb-3">Notifications</h2>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success">@TempData["Success"]</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <div class="d-flex justify-content-between mb-3">
        <button class="btn btn-secondary" type="button" data-bs-toggle="offcanvas" data-bs-target="#filterOffcanvas">
            <i class="fas fa-filter"></i> Filter
        </button>
        <form method="post" asp-action="MarkAllAsRead">
            @Html.AntiForgeryToken()
            <button class="btn btn-success" type="submit">Mark All as Read</button>
        </form>
    </div>

    <div class="list-group mb-4">
        @foreach (var notification in Model.Notifications)
        {
            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                <div>
                    @notification.Message
                    <div class="text-muted small">📅 @notification.CreatedDate.ToString("yyyy-MM-dd")</div>
                </div>
                <div class="d-flex gap-2 align-items-center">
                    @if (!notification.IsRead)
                    {
                        <form method="post" asp-action="MarkAsRead" asp-route-id="@notification.Id">
                            @Html.AntiForgeryToken()
                            <button class="btn btn-sm btn-outline-primary">Mark as Read</button>
                        </form>

                        @if (notification.IsBirthdayToday)
                        {
                            <form method="post" asp-action="SendWishes" asp-route-id="@notification.Id">
                                @Html.AntiForgeryToken()
                                <button class="btn btn-sm btn-primary" type="submit">Send Wishes</button>
                            </form>
                        }
                    }
                    else
                    {
                        <span class="badge bg-secondary">Read</span>
                        @if (notification.IsBirthdayToday)
                        {
                            <form method="post" asp-action="SendWishes" asp-route-id="@notification.Id">
                                @Html.AntiForgeryToken()
                                <button class="btn btn-sm btn-primary" type="submit">Send Wishes</button>
                            </form>
                        }
                    }
                </div>
            </div>
        }
    </div>

    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            <li class="page-item @(Model.PageNumber == 1 ? "disabled" : "")">
                <a class="page-link"
                   asp-action="Index"
                   asp-route-page="@(Model.PageNumber - 1)"
                   asp-route-username="@Model.UsernameFilter"
                   asp-route-date="@(Model.DateFilter?.ToString("yyyy-MM-dd"))">
                    Previous
                </a>
            </li>
            @for (int i = 1; i <= Model.TotalPages; i++)
            {
                <li class="page-item @(i == Model.PageNumber ? "active" : "")">
                    <a class="page-link"
                       asp-action="Index"
                       asp-route-page="@i"
                       asp-route-username="@Model.UsernameFilter"
                       asp-route-date="@(Model.DateFilter?.ToString("yyyy-MM-dd"))">
                        @i
                    </a>
                </li>
            }
            <li class="page-item @(Model.PageNumber == Model.TotalPages ? "disabled" : "")">
                <a class="page-link"
                   asp-action="Index"
                   asp-route-page="@(Model.PageNumber + 1)"
                   asp-route-username="@Model.UsernameFilter"
                   asp-route-date="@(Model.DateFilter?.ToString("yyyy-MM-dd"))">
                    Next
                </a>
            </li>
        </ul>
    </nav>
</div>

<div class="offcanvas offcanvas-end" tabindex="-1" id="filterOffcanvas">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title">Filter Notifications</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
    </div>
    <div class="offcanvas-body">
        <form method="get" asp-action="Index">
            <div class="mb-3">
                <label for="usernameFilter" class="form-label">Username</label>
                <input type="text" class="form-control" name="username" value="@Model.UsernameFilter" placeholder="Enter name..." />
            </div>
            <div class="mb-3">
                <label for="dateFilter" class="form-label">Date</label>
                <input type="date" class="form-control" name="date" value="@Model.DateFilter?.ToString("yyyy-MM-dd")" />
            </div>
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">Apply</button>
                <a asp-action="Index" class="btn btn-secondary">Reset</a>
            </div>
        </form>
    </div>
</div>