﻿@model IEnumerable<AdressBookAppWeb.Models.Log>
@using AdressBookAppWeb.Models.Enums

@{
    ViewData["Title"] = "User Activity Logs";
    var filters = ViewBag.Filters;
    int page = ViewBag.CurrentPage as int? ?? 1;
    int totalPages = ViewBag.TotalPages as int? ?? 1;
}

<div class="container mt-4">
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 class="mb-0">User Activity Logs</h2>
            <button class="btn btn-outline-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#filterOffcanvas">
                Filters
            </button>
        </div>

        <div class="card-body">
            <table class="table table-striped table-hover mt-4">
                <thead class="table-primary">
                    <tr>
                        <th style="width:50px;">#</th>
                        <th style="width:180px;">Timestamp</th>
                        <th style="width:120px;">Module</th>
                        <th style="width:120px;">Action</th>
                        <th style="width:100px;">Status</th>
                        <th>User</th>
                        <th style="width:100px;"></th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var log in Model)
                    {
                        <tr>
                            <td>@log.Id</td>
                            <td>@log.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")</td>
                            <td>@log.Module</td>
                            <td>@log.Action</td>
                            <td>@log.Status</td>
                            <td>
                                @if (log.User != null)
                                {
                                    @($"{log.User.FirstName} {log.User.LastName}")
                                }
                                else
                                {
                                    <em>System</em>
                                }
                            </td>
                            <td>
                                <a class="btn btn-sm btn-info" asp-action="Details" asp-route-id="@log.Id">Details</a>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

            <nav>
                <ul class="pagination justify-content-center">
                    <li class="page-item @(page <= 1 ? "disabled" : "")">
                        <a class="page-link" href="?page=@(page - 1)">Previous</a>
                    </li>
                    @for (int i = 1; i <= totalPages; i++)
                    {
                        <li class="page-item @(i == page ? "active" : "")">
                            <a class="page-link" href="?page=@i">@i</a>
                        </li>
                    }
                    <li class="page-item @(page >= totalPages ? "disabled" : "")">
                        <a class="page-link" href="?page=@(page + 1)">Next</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<div class="offcanvas offcanvas-end" tabindex="-1" id="filterOffcanvas">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title">Filter Logs</h5>
        <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas"></button>
    </div>
    <div class="offcanvas-body">
        <form method="get">
            <div class="mb-3">
                <label class="form-label">Module</label>
                <select class="form-select" name="module">
                    <option value="">All Modules</option>
                    @foreach (LogModule mod in Enum.GetValues(typeof(LogModule)))
                    {
                        var selected = (filters?.module == mod.ToString()) ? "selected" : null;
                        <option value="@mod" selected="@selected">@mod</option>
                    }
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Action Type</label>
                <select class="form-select" name="actionType">
                    <option value="">All Actions</option>
                    @foreach (LogAction act in Enum.GetValues(typeof(LogAction)))
                    {
                        var selected = (filters?.actionType == act.ToString()) ? "selected" : null;
                        <option value="@act" selected="@selected">@act</option>
                    }
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Status</label>
                <select class="form-select" name="status">
                    <option value="">All Statuses</option>
                    @foreach (LogStatus stat in Enum.GetValues(typeof(LogStatus)))
                    {
                        var selected = (filters?.status == stat.ToString()) ? "selected" : null;
                        <option value="@stat" selected="@selected">@stat</option>
                    }
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Date Range</label>
                <div class="d-flex gap-2">
                    <input type="date" class="form-control" name="dateFrom"
                           value="@(filters?.dateFrom != null ? ((DateTime)filters.dateFrom).ToString("yyyy-MM-dd") : "")" />
                    <input type="date" class="form-control" name="dateTo"
                           value="@(filters?.dateTo != null ? ((DateTime)filters.dateTo).ToString("yyyy-MM-dd") : "")" />
                </div>
            </div>
            <div class="mb-3">
                <label class="form-label">Username</label>
                <input type="text" class="form-control" name="username" value="@filters?.username" />
            </div>
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">Search</button>
                <a href="@Url.Action("Index", "Logs", new { area = "Admin" })" class="btn btn-secondary">Reset</a>
            </div>
        </form>
    </div>
</div>