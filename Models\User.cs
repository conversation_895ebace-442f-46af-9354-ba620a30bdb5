﻿using global::AdressBookAppWeb.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace AdressBookAppWeb.Models
{
    public class User
    {
        public int Id { get; set; }

        [Required]
        public string FirstName { get; set; }

        [Required]
        public string LastName { get; set; }

        [Required]
        public string Email { get; set; }

        [Required]
        public string Password { get; set; }

        [Required]
        public string Phone { get; set; }

        [Required]
        public string UserKey { get; set; }

        public string? ProfileImage { get; set; }

        [Required]
        public UserRole Role { get; set; }

        [Required]
        public DateTime RegistrationDate { get; set; }

        public DateTime? LastLoginDate { get; set; }

        public string? PasswordResetToken { get; set; }
        public DateTime? TokenExpiry { get; set; }

        public ICollection<Contact>? Contacts { get; set; }
        public ICollection<Group>? Groups { get; set; }
        public ICollection<SharedAccess>? OwnedAccesses { get; set; }
        public ICollection<SharedAccess>? ViewableAccesses { get; set; }
        public ICollection<Notification>? Notifications { get; set; }
        public ICollection<Log>? Logs { get; set; }
        public ICollection<ContactReport>? ContactReports { get; set; }
    }
}