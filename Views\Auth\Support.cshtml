﻿@model AdressBookAppWeb.ViewModels.ContactReportViewModel

@{
    Layout = "_AuthLayout";
    ViewData["Title"] = "Contact Support";
}

<div class="col-lg-6">
    <div class="card shadow-lg border-0 rounded-lg mt-5">
        <div class="card-header">
            <h3 class="text-center font-weight-light my-4">Contact Support</h3>
        </div>
        <div class="card-body">
            @if (ViewBag.Success != null)
            {
                <div class="alert alert-success">@ViewBag.Success</div>
            }

            @if (ViewBag.Error != null)
            {
                <div class="alert alert-danger">@ViewBag.Error</div>
            }

            <form asp-action="Support" method="post">
                <div class="form-floating mb-3">
                    <input asp-for="SenderName" class="form-control" placeholder="Enter your name" required />
                    <label asp-for="SenderName">Full Name</label>
                    <span asp-validation-for="SenderName" class="text-danger"></span>
                </div>
                <div class="form-floating mb-3">
                    <input asp-for="Email" class="form-control" placeholder="<EMAIL>" required />
                    <label asp-for="Email">Email Address</label>
                    <span asp-validation-for="Email" class="text-danger"></span>
                </div>
                <div class="form-floating mb-3">
                    <input asp-for="Subject" class="form-control" placeholder="Subject" required />
                    <label asp-for="Subject">Subject</label>
                    <span asp-validation-for="Subject" class="text-danger"></span>
                </div>
                <div class="mb-3">
                    <label asp-for="Message" class="form-label">Message</label>
                    <textarea asp-for="Message" class="form-control" rows="5" placeholder="Type your message..." required></textarea>
                    <span asp-validation-for="Message" class="text-danger"></span>
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">Send Message</button>
                </div>
            </form>
        </div>
        <div class="card-footer text-center py-3">
            <div class="small text-muted">
                For urgent issues, email us at
                <a href="mailto:<EMAIL>" class="text-decoration-underline text-primary"><EMAIL></a>
            </div>
        </div>
    </div>
</div>

@* @section Scripts { *@
@*     @{ *@
@*         await Html.RenderPartialAsync("_ValidationScriptsPartial"); *@
@*     } *@
@* } *@