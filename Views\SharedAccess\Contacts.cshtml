﻿@model List<AdressBookAppWeb.Models.Contact>

@{
    ViewData["Title"] = "Shared Contacts";
    var ownerId = (int)ViewBag.OwnerId;
    var selectedGroup = Context.Request.Query["group"].ToString();
}

<div class="container-fluid px-4">
    <h2 class="mt-4">Shared Contacts</h2>

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <div class="d-flex justify-content-end mb-4">
        <button class="btn btn-primary" data-bs-toggle="offcanvas" data-bs-target="#searchOffcanvas" aria-controls="searchOffcanvas">
            Search & Filter
        </button>
    </div>

    <div class="row g-3">
        @foreach (var contact in Model)
        {
            <div class="col-sm-6 col-md-4">
                <div class="card shadow-sm d-flex flex-row align-items-center p-2" style="border-radius: 8px;">
                    <img src="@(!string.IsNullOrEmpty(contact.ContactImage) ? contact.ContactImage : "/assets/img/default.png")"
                         class="rounded"
                         style="height: 80px; width: 80px; object-fit: cover;"
                         alt="Contact Image">
                    <div class="ms-3 w-100">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <h6 class="mb-0">@contact.Name</h6>
                        </div>
                        <p class="mb-0" style="font-size: 0.85rem;">Phone: @contact.Phone</p>
                        <p class="mb-1" style="font-size: 0.85rem;">Email: @contact.Email</p>
                        <p class="mb-1" style="font-size: 0.85rem;">Group: @(contact.Group?.GroupName ?? "N/A")</p>
                        <div class="d-flex gap-1 mt-2 flex-wrap">
                            <a asp-controller="SharedAccess" asp-action="ContactDetails" asp-route-id="@contact.Id" class="btn btn-info btn-sm">View</a>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>

    <div class="mt-4 d-flex justify-content-center">
        <nav>
            <ul class="pagination">
                @{
                    int totalPages = ViewBag.TotalPages;
                    int currentPage = ViewBag.CurrentPage;
                    int visibleRange = 2;
                    int startPage = Math.Max(1, currentPage - visibleRange);
                    int endPage = Math.Min(totalPages, currentPage + visibleRange);
                }

                <!-- Previous Button -->
                <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                    <a class="page-link" asp-action="Contacts" asp-route-ownerId="@ownerId" asp-route-page="@(currentPage - 1)">Previous</a>
                </li>

                <!-- First page and leading ellipsis -->
                @if (startPage > 2)
                {
                    <li class="page-item">
                        <a class="page-link" asp-action="Contacts" asp-route-ownerId="@ownerId" asp-route-page="1">1</a>
                    </li>
                    <li class="page-item disabled"><span class="page-link">...</span></li>
                }

                <!-- Page range -->
                @for (int i = startPage; i <= endPage; i++)
                {
                    <li class="page-item @(i == currentPage ? "active" : "")">
                        <a class="page-link" asp-action="Contacts" asp-route-ownerId="@ownerId" asp-route-page="@i">@i</a>
                    </li>
                }

                <!-- Trailing ellipsis and last page -->
                @if (endPage < totalPages - 1)
                {
                    <li class="page-item disabled"><span class="page-link">...</span></li>
                    <li class="page-item">
                        <a class="page-link" asp-action="Contacts" asp-route-ownerId="@ownerId" asp-route-page="@totalPages">@totalPages</a>
                    </li>
                }

                <!-- Next Button -->
                <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                    <a class="page-link" asp-action="Contacts" asp-route-ownerId="@ownerId" asp-route-page="@(currentPage + 1)">Next</a>
                </li>
            </ul>
        </nav>
    </div>
</div>

<div class="offcanvas offcanvas-end" tabindex="-1" id="searchOffcanvas" aria-labelledby="searchOffcanvasLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="searchOffcanvasLabel">Search Shared Contacts</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <form id="sharedSearchForm" method="get" asp-action="Contacts" asp-controller="SharedAccess">
            <input type="hidden" name="ownerId" value="@ownerId" />

            <div class="mb-3">
                <label for="fullnameQuery" class="form-label">Fullname</label>
                <input type="text" class="form-control mb-2" id="fullnameQuery" name="fullnameQuery" value="@Context.Request.Query["fullnameQuery"]" placeholder="Enter fullname..." />
            </div>

            <div class="mb-3">
                <label for="emailQuery" class="form-label">Email</label>
                <input type="email" class="form-control mb-2" id="emailQuery" name="email" value="@Context.Request.Query["email"]" placeholder="Enter email..." />
            </div>

            <div class="mb-3">
                <label for="phoneQuery" class="form-label">Phone Number</label>
                <input type="tel" class="form-control mb-2" id="phoneQuery" name="phone" value="@Context.Request.Query["phone"]" placeholder="Enter phone number..." />
            </div>

            <div class="mb-3">
                <label for="groupFilter" class="form-label">Group</label>
                <select class="form-select" id="groupFilter" name="groupId">
                    <option value="">All Groups</option>
                    @if (ViewBag.Groups is List<AdressBookAppWeb.Models.Group> groups)
                    {
                        foreach (var group in groups)
                        {
                            <option value="@group.Id" selected="@(Context.Request.Query["groupId"] == group.Id.ToString())">
                                @group.GroupName
                            </option>
                        }
                    }
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">Birthday Range</label>
                <div class="d-flex gap-2">
                    <input type="date" class="form-control" id="birthdayFrom" name="birthdayFrom" value="@Context.Request.Query["birthdayFrom"]" />
                    <input type="date" class="form-control" id="birthdayTo" name="birthdayTo" value="@Context.Request.Query["birthdayTo"]" />
                </div>
            </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">Search</button>
                <a href="@Url.Action("Contacts", "SharedAccess", new { ownerId = ownerId })" class="btn btn-secondary">Reset</a>
            </div>
        </form>
    </div>
</div>