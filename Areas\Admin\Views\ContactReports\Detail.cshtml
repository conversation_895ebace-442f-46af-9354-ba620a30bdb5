﻿@model AdressBookAppWeb.Models.ContactReport
@using AdressBookAppWeb.Models.Enums

@{
    ViewData["Title"] = "View Contact Report";
}

<div class="container-fluid px-4">
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <h1 class="mt-4">Contact Report Details</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-controller="Dashboard" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-controller="ContactReports" asp-action="Index">Contact Reports</a></li>
        <li class="breadcrumb-item active">View</li>
    </ol>

    <div class="card mb-4 shadow-sm">
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label fw-bold">Sender Name:</label>
                <div>@Model.SenderName</div>
            </div>

            @if (Model.UserId.HasValue)
            {
                <div class="mb-3">
                    <label class="form-label fw-bold">User ID:</label>
                    <div>@Model.UserId</div>
                </div>
            }

            <div class="mb-3">
                <label class="form-label fw-bold">Email:</label>
                <div>@Model.Email</div>
            </div>

            <div class="mb-3">
                <label class="form-label fw-bold">Subject:</label>
                <div>@Model.Subject</div>
            </div>

            <div class="mb-3">
                <label class="form-label fw-bold">Message:</label>
                <div>@Model.Message</div>
            </div>

            <div class="mb-3">
                <label class="form-label fw-bold">Date:</label>
                <div>@Model.ReportDate.ToString("yyyy-MM-dd")</div>
            </div>

            @if (!string.IsNullOrEmpty(Model.AdminResponse))
            {
                <div class="mb-3">
                    <label class="form-label fw-bold text-success">Admin Response:</label>
                    <div class="border rounded p-2">@Model.AdminResponse</div>
                </div>
            }

            <div class="mb-3">
                <form method="post" asp-action="UpdateStatus" class="d-flex align-items-center gap-2">
                    @Html.AntiForgeryToken()
                    <input type="hidden" name="id" value="@Model.Id" />
                    <label class="form-label fw-bold mb-0">Status:</label>
                    <select class="form-select w-auto" name="status">
                        @foreach (ReportStatus s in Enum.GetValues(typeof(ReportStatus)))
                        {
                            var selected = (Model.Status == s) ? "selected" : null;
                            <option value="@s" selected="@selected">@s</option>
                        }
                    </select>
                    <button type="submit" class="btn btn-sm btn-outline-primary">Update</button>
                </form>
            </div>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-header">
            <i class="fas fa-reply me-1"></i> Reply to Sender
        </div>
        <div class="card-body">
            @* Display validation errors for SendReply *@
            @if (!ViewData.ModelState.IsValid)
            {
                <div class="alert alert-danger">
                    @Html.ValidationSummary()
                </div>
            }

            <form method="post" asp-action="SendReply">
                @Html.AntiForgeryToken()
                <input type="hidden" name="reportId" value="@Model.Id" />

                <div class="mb-3">
                    <label for="emailTo" class="form-label">To</label>
                    <input type="email" class="form-control" id="emailTo" name="emailTo" value="@Model.Email" readonly />
                    @Html.ValidationMessage("emailTo", "", new { @class = "text-danger" })
                </div>

                <div class="mb-3">
                    <label for="subject" class="form-label">Subject</label>
                    <input type="text" class="form-control" id="subject" name="subject" value="Re: @Model.Subject" />
                    @Html.ValidationMessage("subject", "", new { @class = "text-danger" })
                </div>

                <div class="mb-3">
                    <label for="message" class="form-label">Message</label>
                    <textarea class="form-control" id="message" name="message" rows="6" placeholder="Type your reply here..."></textarea>
                    @Html.ValidationMessage("message", "", new { @class = "text-danger" })
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-success">Send Reply</button>
                    <a asp-controller="ContactReports" asp-action="Index" class="btn btn-secondary">Back</a>
                </div>
            </form>
        </div>
    </div>
</div>