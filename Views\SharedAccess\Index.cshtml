﻿@using AdressBookAppWeb.Models.Enums
@model List<AdressBookAppWeb.Models.SharedAccess>

@{
    ViewData["Title"] = "Shared Access";
    var currentPage = (int)(ViewBag.CurrentPage ?? 1);
    var totalPages = (int)(ViewBag.TotalPages ?? 1);
    var searchTerm = ViewBag.SearchTerm as string;
    var accessStatus = ViewBag.AccessStatus as string;
}

<div class="container-fluid px-4">
    <h2 class="mt-4">Shared Access</h2>

    @* Alerts *@
    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success">@TempData["Message"]</div>
    }
    @if (TempData["Warning"] != null)
    {
        <div class="alert alert-warning">@TempData["Warning"]</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <ul class="nav nav-tabs" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link active" asp-action="Index" asp-controller="SharedAccess">Manage Access</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" asp-action="ContactsSharedWithMe" asp-controller="SharedAccess">Contacts Shared With Me</a>
        </li>
    </ul>

    <div class="tab-content mt-3" id="myTabContent">
        <div class="tab-pane fade show active">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <a asp-controller="Contacts" asp-action="SearchUser" class="btn btn-primary">Add User to Share</a>
                <button class="btn btn-secondary" data-bs-toggle="offcanvas" data-bs-target="#filterManageAccess">
                    Filter Users
                </button>
            </div>

            <div class="row gy-3">
                @foreach (var user in Model)
                {
                    <div class="col-sm-6 col-md-4">
                        <div class="card shadow-sm d-flex flex-row align-items-center p-2" style="border-radius: 8px;">
                            <img src="@(!string.IsNullOrEmpty(user.Viewer?.ProfileImage) ? user.Viewer.ProfileImage : "/assets/img/default.png")"
                                 class="rounded"
                                 style="height: 80px; width: 80px; object-fit: cover;"
                                 alt="User Image" />
                            <div class="ms-3 w-100">
                                <div class="mb-1">
                                    <h6 class="mb-0">@user.Viewer.FirstName @user.Viewer.LastName</h6>
                                    <p class="mb-0">@user.Viewer.Email</p>
                                </div>
                                <div class="d-flex gap-1 mt-2 flex-wrap">
                                    @if (user.AccessStatus == AccessStatus.Accessed)
                                    {
                                        <form method="post" asp-action="Revoke" asp-controller="SharedAccess" asp-route-id="@user.Id" class="d-inline">
                                            @Html.AntiForgeryToken()
                                            <button type="submit" class="btn btn-sm btn-outline-danger">Revoke Access</button>
                                        </form>
                                    }
                                    else
                                    {
                                        <form method="post" asp-action="Grant" asp-controller="SharedAccess" asp-route-id="@user.Id" class="d-inline">
                                            @Html.AntiForgeryToken()
                                            <button type="submit" class="btn btn-sm btn-outline-success">Grant Access</button>
                                        </form>
                                    }

                                    @* Remove User *@
                                    <form method="post" asp-action="Remove" asp-controller="SharedAccess" asp-route-id="@user.Id" class="d-inline">
                                        @Html.AntiForgeryToken()
                                        <button type="submit" class="btn btn-sm btn-danger">Remove</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>

            @if (totalPages > 1)
            {
                <div class="d-flex justify-content-center mt-4">
                    <nav aria-label="Manage Access Pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                <a class="page-link" asp-action="Index"
                                   asp-route-page="@(currentPage - 1)"
                                   asp-route-searchTerm="@searchTerm"
                                   asp-route-accessStatus="@accessStatus">
                                    Previous
                                </a>
                            </li>

                            @for (int i = 1; i <= totalPages; i++)
                            {
                                <li class="page-item @(i == currentPage ? "active" : "")">
                                    <a class="page-link" asp-action="Index"
                                       asp-route-page="@i"
                                       asp-route-searchTerm="@searchTerm"
                                       asp-route-accessStatus="@accessStatus">@i</a>
                                </li>
                            }

                            <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                <a class="page-link" asp-action="Index"
                                   asp-route-page="@(currentPage + 1)"
                                   asp-route-searchTerm="@searchTerm"
                                   asp-route-accessStatus="@accessStatus">
                                    Next
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            }
        </div>
    </div>
</div>

@* Offcanvas sidebar for filtering Manage Access *@
<div class="offcanvas offcanvas-end" tabindex="-1" id="filterManageAccess" aria-labelledby="filterManageAccessLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="filterManageAccessLabel">Filter Users</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <form method="get" asp-action="Index" asp-controller="SharedAccess">
            <div class="mb-3">
                <label for="manageAccessName" class="form-label">Fullname or Email</label>
                <input type="text" name="searchTerm" id="manageAccessName" class="form-control"
                       placeholder="Enter name or email..." value="@searchTerm" />
            </div>
            <div class="mb-3">
                <label for="accessStatusFilter" class="form-label">Access Status</label>
                <select name="accessStatus" id="accessStatusFilter" class="form-select">
                    <option value="" selected="@string.IsNullOrEmpty(accessStatus)">All</option>
                    <option value="Accessed" selected="@(accessStatus == "Accessed")">Accessed</option>
                    <option value="Revoked" selected="@(accessStatus == "Revoked")">Revoked</option>
                </select>
            </div>
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">Search</button>
                <a asp-action="Index" class="btn btn-secondary">Reset</a>
            </div>
        </form>
    </div>
</div>