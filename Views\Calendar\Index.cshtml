﻿@{
    ViewData["Title"] = "Calendar";
}

<div class="container-fluid px-4">
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger mt-4">@TempData["Error"]</div>
    }

    <div class="calendar-section mt-4">
        <h2 class="mb-4">Calendar View</h2>
        <div style="max-height: 70vh; overflow: auto;" class="calendar-wrapper border rounded p-3">
            <div id="calendar"></div>
        </div>
    </div>
</div>

<div class="modal fade" id="dayClickModal" tabindex="-1" aria-labelledby="dayClickModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="dayClickModalLabel">Contacts on <span id="clickedDateDisplay"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Do you want to view contacts related to this date?</p>
            </div>
            <div class="modal-footer">
                <a asp-controller="Contacts" asp-action="ContactDetail" class="btn btn-primary" id="viewContactBtn">
                    View Contact
                </a>
                <button type="button" class="btn btn-success" id="sayHappyBirthdayBtn" style="display: none;">
                    Say Happy Birthday
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.17/index.global.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
      var calendarEl = document.getElementById('calendar');
      var calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth,timeGridWeek,timeGridDay'
        },
        events: 'Calendar/GetBirthdays',
        eventClick: function(info) {
          const ev = info.event;
          const clickedDate = ev.startStr;

          const contactId   = ev.extendedProps.contactId;

          document.getElementById("clickedDateDisplay").innerText = clickedDate;

          const today = new Date().toISOString().slice(0,10);
          document.getElementById("sayHappyBirthdayBtn").style.display =
            (clickedDate === today) ? 'inline-block' : 'none';

          document.getElementById("viewContactBtn").href = `/Contacts/ContactDetail/${contactId}`;

          new bootstrap.Modal(document.getElementById("dayClickModal")).show();
        }
      });


      calendar.render();
    });
</script>