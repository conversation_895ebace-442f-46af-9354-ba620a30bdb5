﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using AdressBookAppWeb.Data;

namespace AdressBookAppWeb.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class BaseController : Controller
    {
        protected readonly AppDbContext _context;

        public BaseController(AppDbContext context)
        {
            _context = context;
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId.HasValue)
                {
                    var user = _context.Users.FirstOrDefault(u => u.Id == userId.Value);
                    ViewBag.CurrentUser = user;
                }
            }
            catch (Exception)
            {
                ViewBag.ErrorMessage = "Unable to load current user. Some features may not work.";
                TempData["ErrorMessage"] = "Unable to load current user. Some features may not work.";
            }

            base.OnActionExecuting(context);
        }
    }
}