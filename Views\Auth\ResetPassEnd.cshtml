﻿@{
    Layout = "_AuthLayout";
    ViewData["Title"] = "Reset Password";
}

<div class="container-fluid px-4 mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h4 class="card-title mb-3 text-center">Reset Your Password</h4>

                    <form asp-action="ResetPassEnd" method="post">
                        <input type="hidden" name="token" value="@ViewBag.Token" />

                        <div class="mb-3">
                            <label for="NewPassword" class="form-label">New Password</label>
                            <input type="password" name="newPassword" class="form-control" required />
                        </div>

                        <div class="mb-3">
                            <label for="ConfirmPassword" class="form-label">Confirm Password</label>
                            <input type="password" name="confirmPassword" class="form-control" required />
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Set New Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
