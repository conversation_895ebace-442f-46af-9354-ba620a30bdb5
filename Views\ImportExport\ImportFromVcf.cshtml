﻿@{
    ViewData["Title"] = "Import from VCF";
}

<div class="container-fluid px-4">
    <h2 class="mt-4">Import Contacts from VCF</h2>
    <p class="text-muted">Upload a `.vcf` (vCard) file exported from a phone or contact app.</p>

    @* Success/Error/Warning Alerts *@
    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success">@TempData["Message"]</div>
    }
    @if (TempData["Warning"] != null)
    {
        <div class="alert alert-warning">@TempData["Warning"]</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <div class="row">
        <div class="col-md-8 offset-md-2">
            <form asp-action="ImportFromVcf" method="post" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="vcfFile" class="form-label">Select VCF File</label>
                    <input type="file" class="form-control" name="vcfFile" accept=".vcf" required />
                </div>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-success">Upload & Import</button>
                    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
                </div>
            </form>

            <div class="mt-4">
                <h6>📌 VCF Example:</h6>
                <pre class="bg-light p-2 rounded small">
BEGIN:VCARD
VERSION:3.0
FN:John Doe
TEL:+*********
EMAIL:<EMAIL>
ADR:;;123 Main St
ORG:MyCompany
NOTE:Tag1,Tag2
BDAY:1980-12-01
END:VCARD
                </pre>
            </div>
        </div>
    </div>
</div>