﻿@model AdressBookAppWeb.Models.Contact

@{
    ViewData["Title"] = "Contact Detail";
}

<div class="container-fluid px-4">
    <h2 class="mt-4">Contact Details</h2>

    @* Alerts *@
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-4 text-center">
                    <img src="@(!string.IsNullOrEmpty(Model.ContactImage) ? Model.ContactImage : "/assets/img/default.png")"
                         alt="User Image" class="img-fluid rounded-circle mb-3"
                         style="width: 150px; height: 150px; object-fit: cover;" />
                    <h4>@Model.FirstName @Model.LastName</h4>
                    <p class="text-muted">@Model.Position</p>
                </div>

                <div class="col-md-4">
                    <h5>Contact Information</h5>
                    <p><strong>Email:</strong> @Model.Email</p>
                    <p><strong>Phone:</strong> @Model.Phone</p>
                    <p><strong>Address:</strong> @Model.Address</p>
                </div>

                <div class="col-md-4">
                    <h5>Additional Information</h5>
                    <p><strong>Company:</strong> @Model.Company</p>
                    <p><strong>Group:</strong> @(Model.Group?.GroupName ?? "-")</p>
                    <p><strong>Tags:</strong> @Model.Tags</p>
                    <p><strong>Birthday:</strong> @(Model.BirthDate?.ToString("yyyy-MM-dd") ?? "-")</p>
                </div>
            </div>
        </div>
    </div>
</div>