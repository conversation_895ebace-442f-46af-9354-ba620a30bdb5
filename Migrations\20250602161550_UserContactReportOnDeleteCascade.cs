﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AdressBookAppWeb.Migrations
{
    /// <inheritdoc />
    public partial class UserContactReportOnDeleteCascade : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ContactReports_Users_UserId",
                table: "ContactReports");

            migrationBuilder.AddForeignKey(
                name: "FK_ContactReports_Users_UserId",
                table: "ContactReports",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ContactReports_Users_UserId",
                table: "ContactReports");

            migrationBuilder.AddForeignKey(
                name: "FK_ContactReports_Users_UserId",
                table: "ContactReports",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
