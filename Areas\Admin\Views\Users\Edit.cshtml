﻿@using AdressBookAppWeb.Models.Enums
@model AdressBookAppWeb.ViewModels.UserEditViewModel

@{
    ViewData["Title"] = "Edit User";
    var currentUser = ViewBag.CurrentUser as AdressBookAppWeb.Models.User;
    bool isSuperAdmin = currentUser?.Role == UserRole.SuperAdmin;
}

<div class="container-fluid px-4">
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <h1 class="mt-4">Edit User</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Dashboard" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Users" asp-action="Index">Users</a></li>
        <li class="breadcrumb-item active">Edit</li>
    </ol>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            @if (!ViewData.ModelState.IsValid)
            {
                <div class="alert alert-danger">
                    @Html.ValidationSummary(false, "", new { @class = "text-danger" })
                </div>
            }

            <form method="post" asp-action="Edit" enctype="multipart/form-data">
                @Html.AntiForgeryToken()
                <input type="hidden" name="Id" value="@Model.Id" />

                <div class="mb-3">
                    <label asp-for="FirstName" class="form-label">First Name</label>
                    <input asp-for="FirstName" class="form-control" />
                    <span asp-validation-for="FirstName" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="LastName" class="form-label">Last Name</label>
                    <input asp-for="LastName" class="form-control" />
                    <span asp-validation-for="LastName" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="Email" class="form-label">Email</label>
                    <input asp-for="Email" class="form-control" />
                    <span asp-validation-for="Email" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="Phone" class="form-label">Phone</label>
                    <input asp-for="Phone" class="form-control" />
                    <span asp-validation-for="Phone" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label class="form-label">Role</label>
                    @if (isSuperAdmin && currentUser?.Id != Model.Id)
                    {
                        <select asp-for="Role" class="form-select">
                            <option value="Admin" selected="@(Model.Role == UserRole.Admin ? "selected" : null)">Admin</option>
                            <option value="User" selected="@(Model.Role == UserRole.User ? "selected" : null)">User</option>
                        </select>
                    }
                    else
                    {
                        <input type="text" class="form-control" value="@Model.Role.ToString()" disabled />
                        <input type="hidden" name="Role" value="@Model.Role" />
                    }
                    <span asp-validation-for="Role" class="text-danger"></span>
                </div>

                @if (!string.IsNullOrEmpty(Model.ProfileImagePath))
                {
                    <div class="mb-3">
                        <label class="form-label">Current Profile Image:</label><br />
                        <img src="@Model.ProfileImagePath" class="rounded-circle mb-2" width="100" height="100" alt="Current Profile Image" />
                    </div>
                }

                <div class="mb-3">
                    <label for="profileImage" class="form-label">Change Profile Image</label>
                    <input class="form-control" type="file" id="profileImage" name="ProfileImage" accept="image/*" />
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-success">Save Changes</button>
                    <a asp-area="Admin" asp-controller="Users" asp-action="Index" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>