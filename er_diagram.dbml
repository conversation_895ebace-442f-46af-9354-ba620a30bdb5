Enum UserRole {
  SuperAdmin
  Admin
  User
}

Enum RequestType {
  AddToContacts
  ViewContacts
  GiveAccess
}

Enum Direction {
  Incoming
  Outgoing
}

Enum RequestStatus {
  Pending
  Accepted
  Rejected
}

Enum AccessStatus {
  Accessed
  Revoked
}

Enum NotificationType {
  Birthday
  AccessRequest
  SystemNotice
}

Enum LogModule {
  System
  Contacts
  Groups
  Calendar
  SharedAccess
  Profile
  ContactUs
  Auth
}

Enum LogAction {
  Add
  Edit
  Delete
  LoggedIn
  LoggedOut
  GiveAccess
  RevokeAccess
  View
}

Enum ReportStatus {
  Pending
  Reviewed
  Resolved
}

Table User {
  Id int [pk]
  FirstName varchar [not null]
  LastName varchar [not null]
  Email varchar [not null]
  Password varchar [not null]
  Phone varchar [not null]
  UserKey varchar [not null]
  ProfileImage varchar
  Role UserRole [not null]
  RegistrationDate datetime [not null]
  LastLoginDate datetime
}

Table Contact {
  Id int [pk]
  UserId int [not null]
  Name varchar [not null]
  Position varchar
  Email varchar
  Phone varchar
  Address varchar
  Company varchar
  ContactImage varchar
  IsStarred boolean [not null]
  Tags varchar
  BirthDate datetime
  CreatedDate datetime [not null]
  UpdatedDate datetime
}

Table Group {
  Id int [pk]
  UserId int [not null]
  GroupName varchar [not null]
  GroupDescription varchar
  CreatedDate datetime [not null]
  UpdatedDate datetime
}

Table ContactGroup {
  Id int [pk]
  ContactId int [not null]
  GroupId int [not null]
}

Table AccessRequest {
  Id int [pk]
  RequesterId int [not null]
  ReceiverId int [not null]
  RequestType RequestType [not null]
  Direction Direction [not null]
  Status RequestStatus [not null]
  RequestDate datetime [not null]
  AcceptDate datetime
  RejectDate datetime
}

Table SharedAccess {
  Id int [pk]
  OwnerId int [not null]
  ViewerId int [not null]
  AccessStatus AccessStatus [not null]
  GrantedDate datetime
  RevokedDate datetime
}

Table Notification {
  Id int [pk]
  UserId int [not null]
  ContactId int
  Type NotificationType [not null]
  Message varchar [not null]
  IsRead boolean [not null]
  CreatedDate datetime [not null]
  ReadDate datetime
}

Table Log {
  Id int [pk]
  UserId int
  Module LogModule [not null]
  Action LogAction [not null]
  Message varchar [not null]
  LogDate datetime [not null]
  IPAddress varchar [not null]
}

Table ContactReport {
  Id int [pk]
  UserId int [not null]
  SenderName varchar [not null]
  Email varchar [not null]
  Subject varchar [not null]
  Message varchar [not null]
  AdminResponse varchar
  Status ReportStatus [not null]
  ReportDate datetime [not null]
  ReviewDate datetime
}

Ref: "User"."Id" < "Contact"."UserId"
Ref: "User"."Id" < "Group"."UserId"
Ref: "User"."Id" < "AccessRequest"."RequesterId"
Ref: "User"."Id" < "AccessRequest"."ReceiverId"
Ref: "User"."Id" < "SharedAccess"."OwnerId"
Ref: "User"."Id" < "SharedAccess"."ViewerId"
Ref: "User"."Id" < "Notification"."UserId"
Ref: "User"."Id" < "Log"."UserId"
Ref: "User"."Id" < "ContactReport"."UserId"

Ref: "Contact"."Id" < "ContactGroup"."ContactId"
Ref: "Contact"."Id" < "Notification"."ContactId"

Ref: "Group"."Id" < "ContactGroup"."GroupId"