﻿@model AdressBookAppWeb.ViewModels.AddGroupViewModel
@{
    ViewData["Title"] = "Add Group";
}

<div class="container-fluid px-4 pb-4">
    <h2 class="mt-4 mb-4">Add New Group</h2>

    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success">@TempData["Message"]</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <form asp-action="AddGroup" method="post">
        @Html.AntiForgeryToken()

        <div class="mb-3">
            <label asp-for="GroupName" class="form-label">Group Name*</label>
            <input asp-for="GroupName" class="form-control" placeholder="Enter group name (e.g., Family, Work)" />
            <span asp-validation-for="GroupName" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="GroupDescription" class="form-label">Description</label>
            <textarea asp-for="GroupDescription" class="form-control" rows="3" placeholder="Enter a short description (optional)"></textarea>
            <span asp-validation-for="GroupDescription" class="text-danger"></span>
        </div>

        <button type="submit" class="btn btn-primary">Add Group</button>
        <a asp-action="Index" class="btn btn-secondary ms-2">Cancel</a>
    </form>
</div>