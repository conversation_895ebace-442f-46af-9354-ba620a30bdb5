﻿@using AdressBookAppWeb.Models.Enums
@model List<AdressBookAppWeb.Models.User>

@{
    ViewData["Title"] = "Manage Users";
    var filters = (dynamic)ViewBag.Filters;
    int currentPage = ViewBag.CurrentPage != null ? (int)ViewBag.CurrentPage : 1;
    int totalPages = ViewBag.TotalPages != null ? (int)ViewBag.TotalPages : 1;
    int serialNumber = (currentPage - 1) * 10 + 1;
    var currentUser = ViewBag.CurrentUser as AdressBookAppWeb.Models.User;
}

<div class="container-fluid px-4">
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <h1 class="mt-4">Users</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a asp-area="Admin" asp-controller="Dashboard" asp-action="Index">Dashboard</a></li>
        <li class="breadcrumb-item active">Users</li>
    </ol>

    <div class="d-flex justify-content-between mb-3 flex-wrap gap-2">
        <div>
            <a asp-area="Admin" asp-controller="Users" asp-action="Add" class="btn btn-success">
                <i class="fas fa-user-plus"></i> Add User
            </a>
        </div>
        <div>
            <button class="btn btn-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#filterOffcanvas" aria-controls="filterOffcanvas">
                <i class="fas fa-filter"></i> Filter Users
            </button>
        </div>
    </div>

    <!-- Filter Sidebar -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="filterOffcanvas" aria-labelledby="filterOffcanvasLabel">
        <div class="offcanvas-header">
            <h5 id="filterOffcanvasLabel">Filter Users</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <form method="get">
                <div class="mb-3">
                    <label for="fullNameFilter" class="form-label">Full Name</label>
                    <input type="text" class="form-control" id="fullNameFilter" name="fullName" value="@filters.fullName" placeholder="Enter full name" />
                </div>
                <div class="mb-3">
                    <label for="emailFilter" class="form-label">Email</label>
                    <input type="email" class="form-control" id="emailFilter" name="email" value="@filters.email" placeholder="Enter email" />
                </div>
                <div class="mb-3">
                    <label for="roleFilter" class="form-label">Role</label>
                    <select class="form-select" id="roleFilter" name="role">
                        <option value="">All Roles</option>
                        @foreach (UserRole r in Enum.GetValues(typeof(UserRole)))
                        {
                            <option value="@r" selected="@(filters.role == r.ToString() ? "selected" : null)">
                                @r
                            </option>
                        }
                    </select>
                </div>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">Search</button>
                    <a asp-action="Index" class="btn btn-secondary">Reset</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-users me-1"></i> Users List
        </div>
        <div class="card-body">
            <table class="table table-bordered table-striped align-middle">
                <thead>
                    <tr>
                        <th style="width: 50px;">#</th>
                        <th style="width: 60px;">Profile</th>
                        <th>Full Name</th>
                        <th>Email</th>
                        <th style="width: 120px;">Role</th>
                        <th style="width: 240px;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model != null && Model.Count > 0)
                    {
                        foreach (var user in Model)
                        {
                            <tr>
                                <td>@(serialNumber++)</td>
                                <td>
                                    <img src="@(string.IsNullOrEmpty(user.ProfileImage) ? "/assets/img/default.png" : user.ProfileImage)"
                                         class="rounded-circle"
                                         alt="Profile Image"
                                         width="50"
                                         height="50" />
                                </td>
                                <td>@user.FirstName @user.LastName</td>
                                <td>@user.Email</td>
                                <td>@user.Role</td>
                                <td>
                                    <div class="d-flex gap-1 flex-wrap">
                                        <a asp-area="Admin"
                                           asp-controller="Users"
                                           asp-action="Details"
                                           asp-route-id="@user.Id"
                                           class="btn btn-sm btn-info">
                                            View
                                        </a>

                                        @* Edit permissions *@
                                        @if ((currentUser.Role == UserRole.SuperAdmin) ||
                                       (currentUser.Role == UserRole.Admin && user.Role == UserRole.User))
                                        {
                                            <a asp-area="Admin"
                                               asp-controller="Users"
                                               asp-action="Edit"
                                               asp-route-id="@user.Id"
                                               class="btn btn-sm btn-warning">
                                                Edit
                                            </a>
                                        }

                                        @* Delete/Reset only if allowed *@
                                        @if ((currentUser.Role == UserRole.SuperAdmin && user.Role != UserRole.SuperAdmin) ||
                                       (currentUser.Role == UserRole.Admin && user.Role == UserRole.User))
                                        {
                                            <form asp-area="Admin"
                                                  asp-controller="Users"
                                                  asp-action="Delete"
                                                  asp-route-id="@user.Id"
                                                  method="post"
                                                  class="d-inline"
                                                  onsubmit="return confirm('Are you sure you want to delete this user?');">
                                                <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                            </form>

                                            <form asp-area="Admin"
                                                  asp-controller="Users"
                                                  asp-action="ResetPassword"
                                                  asp-route-id="@user.Id"
                                                  method="post"
                                                  class="d-inline">
                                                <button type="submit" class="btn btn-sm btn-secondary">Reset Password</button>
                                            </form>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td colspan="6" class="text-center">No users found.</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-center mt-3">
            <nav aria-label="Users pagination">
                <ul class="pagination">
                    <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                        <a class="page-link"
                           asp-action="Index"
                           asp-route-page="@(currentPage - 1)"
                           asp-route-fullName="@filters.fullName"
                           asp-route-email="@filters.email"
                           asp-route-role="@filters.role">
                            Previous
                        </a>
                    </li>

                    @for (int i = 1; i <= totalPages; i++)
                    {
                        <li class="page-item @(i == currentPage ? "active" : "")">
                            <a class="page-link"
                               asp-action="Index"
                               asp-route-page="@i"
                               asp-route-fullName="@filters.fullName"
                               asp-route-email="@filters.email"
                               asp-route-role="@filters.role">
                                @i
                            </a>
                        </li>
                    }

                    <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                        <a class="page-link"
                           asp-action="Index"
                           asp-route-page="@(currentPage + 1)"
                           asp-route-fullName="@filters.fullName"
                           asp-route-email="@filters.email"
                           asp-route-role="@filters.role">
                            Next
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>