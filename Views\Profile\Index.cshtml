﻿@model AdressBookAppWeb.ViewModels.UserProfileViewModel
@using AdressBookAppWeb.Models.Enums
@{
    ViewData["Title"] = "User Profile";
    var user = ViewBag.User as AdressBookAppWeb.Models.User;
}

<div class="container-fluid px-4 mt-4 pb-4">
    <h2 class="mb-4">User Profile</h2>

    @* Success/Error Alerts *@
    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success">@TempData["Success"]</div>
    }
    @if (TempData["PasswordSuccess"] != null)
    {
        <div class="alert alert-success">@TempData["PasswordSuccess"]</div>
    }
    @if (TempData["PasswordError"] != null)
    {
        <div class="alert alert-danger">@TempData["PasswordError"]</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <div class="row gy-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5>Profile Information</h5>
                </div>
                <div class="card-body">
                    <form asp-action="UpdateProfile" method="post" enctype="multipart/form-data">
                        @Html.AntiForgeryToken()
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="CurrentImagePath" />

                        <div class="mb-3 text-center">
                            <img src="@(!string.IsNullOrEmpty(Model.CurrentImagePath) ? Model.CurrentImagePath : "/assets/img/default.png")"
                                 alt="Profile Image" class="rounded-circle mb-3"
                                 style="width: 120px; height: 120px; object-fit: cover;" />
                            <div>
                                <label asp-for="ProfileImage" class="form-label">Update Profile Image</label>
                                <input asp-for="ProfileImage" type="file" class="form-control" accept="image/*" />
                                <span asp-validation-for="ProfileImage" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <p><strong>User Key:</strong> @user?.UserKey</p>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Email" class="form-label">Email *</label>
                            <input asp-for="Email" class="form-control" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="FirstName" class="form-label">First Name *</label>
                            <input asp-for="FirstName" class="form-control" />
                            <span asp-validation-for="FirstName" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="LastName" class="form-label">Last Name *</label>
                            <input asp-for="LastName" class="form-control" />
                            <span asp-validation-for="LastName" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Phone" class="form-label">Phone *</label>
                            <input asp-for="Phone" class="form-control" />
                            <span asp-validation-for="Phone" class="text-danger"></span>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5>Change Password</h5>
                </div>
                <div class="card-body">
                    <form asp-action="ChangePassword" method="post">
                        @Html.AntiForgeryToken()
                        <input type="hidden" name="Id" value="@Model.Id" />

                        <div class="mb-3">
                            <label for="CurrentPassword" class="form-label">Current Password *</label>
                            <input name="CurrentPassword" type="password" class="form-control" />
                        </div>

                        <div class="mb-3">
                            <label for="NewPassword" class="form-label">New Password *</label>
                            <input name="NewPassword" type="password" class="form-control" />
                        </div>

                        <div class="mb-3">
                            <label for="ConfirmPassword" class="form-label">Confirm New Password *</label>
                            <input name="ConfirmPassword" type="password" class="form-control" />
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">Update Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-12">
            <div class="card mt-4">
                <div class="card-header">
                    <h5>Sent Reports</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Subject</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var r in user?.ContactReports.OrderByDescending(r => r.ReportDate) ?? Enumerable.Empty<AdressBookAppWeb.Models.ContactReport>())
                            {
                                <tr>
                                    <td>@r.ReportDate.ToString("yyyy-MM-dd")</td>
                                    <td>@r.Subject</td>
                                    <td>
                                        <span class="badge bg-@GetStatusClass(r.Status)">
                                            @r.Status
                                        </span>
                                    </td>
                                    <td>
                                        <a asp-action="Report" asp-route-id="@r.Id" class="btn btn-sm btn-info">View</a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    private string GetStatusClass(ReportStatus status) => status switch
    {
        ReportStatus.Pending => "warning text-dark",
        ReportStatus.Reviewed => "success",
        _ => "secondary"
    };
}