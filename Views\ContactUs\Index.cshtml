﻿@model AdressBookAppWeb.ViewModels.ContactUsViewModel

@{
    ViewData["Title"] = "Contact Us";
}

<div class="container-fluid px-4">
    <div class="row justify-content-center mt-5">
        <div class="col-md-8">
            <div class="card shadow-lg p-4 border-0 rounded-3">
                <h2 class="text-center mb-4">Contact Us</h2>

                @if (TempData["Success"] != null)
                {
                    <div class="alert alert-success">@TempData["Success"]</div>
                }
                @if (TempData["Error"] != null)
                {
                    <div class="alert alert-danger">@TempData["Error"]</div>
                }

                <form asp-action="Index" method="post">
                    @Html.AntiForgeryToken()

                    <div class="mb-3">
                        <label asp-for="Name" class="form-label"></label>
                        <input asp-for="Name" class="form-control" placeholder="Enter your name" />
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Email" class="form-label"></label>
                        <input asp-for="Email" class="form-control" placeholder="Enter your email" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Subject" class="form-label"></label>
                        <input asp-for="Subject" class="form-control" placeholder="Enter subject" />
                        <span asp-validation-for="Subject" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Message" class="form-label"></label>
                        <textarea asp-for="Message" class="form-control" rows="5" placeholder="Enter your message"></textarea>
                        <span asp-validation-for="Message" class="text-danger"></span>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Send Message</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>