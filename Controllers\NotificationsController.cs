﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using AdressBookAppWeb.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace AdressBookAppWeb.Controllers
{
    public class NotificationsController : BaseController
    {
        private const int PageSize = 5;

        public NotificationsController(AppDbContext context) : base(context)
        {
        }

        [HttpGet]
        public async Task<IActionResult> Index(string username, DateTime? date, int page = 1)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to view notifications.";
                    return RedirectToAction("Login", "Auth");
                }

                var query = _context.Notifications
                    .Include(n => n.User)
                    .Include(n => n.Contact)
                    .Where(n => n.UserId == userId.Value)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(username))
                {
                    query = query.Where(n => n.Contact != null && n.Contact.Name.Contains(username));
                }

                if (date.HasValue)
                {
                    query = query.Where(n => n.CreatedDate.Date == date.Value.Date);
                }

                int totalCount = await query.CountAsync();

                var notificationsRaw = await query
                    .OrderByDescending(n => n.CreatedDate)
                    .Skip((page - 1) * PageSize)
                    .Take(PageSize)
                    .ToListAsync();

                var today = DateTime.Today;

                var notifications = notificationsRaw.Select(n => new NotificationDisplayViewModel
                {
                    Id = n.Id,
                    IsRead = n.IsRead,
                    CreatedDate = n.CreatedDate,
                    Message = $"🎂 {n.Contact?.Name}'s birthday is today! 🎉",
                    IsBirthdayToday = n.Contact?.BirthDate?.Month == today.Month &&
                                      n.Contact?.BirthDate?.Day == today.Day
                }).ToList();

                var viewModel = new NotificationListViewModel
                {
                    Notifications = notifications,
                    UsernameFilter = username,
                    DateFilter = date,
                    PageNumber = page,
                    TotalPages = (int)Math.Ceiling(totalCount / (double)PageSize)
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ViewNotifications,
                    $"Exception in Index: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Notifications);

                TempData["Error"] = "An error occurred while loading notifications.";
                return View(new NotificationListViewModel
                {
                    Notifications = new List<NotificationDisplayViewModel>(),
                    PageNumber = 1,
                    TotalPages = 1
                });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MarkAsRead(int id)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to mark notifications as read.";
                    return RedirectToAction("Login", "Auth");
                }

                var notification = await _context.Notifications
                    .FirstOrDefaultAsync(n => n.Id == id && n.UserId == userId.Value);

                if (notification != null && !notification.IsRead)
                {
                    notification.IsRead = true;
                    notification.ReadDate = DateTime.Now;
                    await _context.SaveChangesAsync();
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.MarkNotificationRead,
                    $"Exception in MarkAsRead: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Notifications);

                TempData["Error"] = "An error occurred while marking as read.";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MarkAllAsRead()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to mark notifications as read.";
                    return RedirectToAction("Login", "Auth");
                }

                var notifications = await _context.Notifications
                    .Where(n => !n.IsRead && n.UserId == userId.Value)
                    .ToListAsync();

                foreach (var n in notifications)
                {
                    n.IsRead = true;
                    n.ReadDate = DateTime.Now;
                }

                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.MarkAllNotificationsRead,
                    $"Exception in MarkAllAsRead: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Notifications);

                TempData["Error"] = "An error occurred while marking all as read.";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SendWishes(int id, [FromServices] IConfiguration config)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to send wishes.";
                    return RedirectToAction("Login", "Auth");
                }

                var notification = await _context.Notifications
                    .Include(n => n.Contact)
                    .Include(n => n.User)
                    .FirstOrDefaultAsync(n => n.Id == id && n.UserId == userId.Value);

                if (notification == null)
                {
                    TempData["Error"] = "Notification not found.";
                    return RedirectToAction(nameof(Index));
                }

                if (notification.Contact == null || notification.User == null)
                {
                    TempData["Error"] = "Unable to send wishes: missing contact or user.";
                    return RedirectToAction(nameof(Index));
                }

                var today = DateTime.Today;
                var birthDate = notification.Contact.BirthDate;
                if (birthDate == null ||
                    birthDate.Value.Day != today.Day ||
                    birthDate.Value.Month != today.Month)
                {
                    TempData["Error"] = "It's not their birthday today!";
                    return RedirectToAction(nameof(Index));
                }

                var userFullName = $"{notification.User.FirstName} {notification.User.LastName}";
                var toEmail = notification.Contact.Email;

                if (string.IsNullOrEmpty(toEmail))
                {
                    TempData["Error"] = "Contact does not have an email address.";
                    return RedirectToAction(nameof(Index));
                }

                var emailService = new EmailService(config);
                await emailService.SendEmailAsync(
                    toEmail: toEmail,
                    subject: "🎉 Happy Birthday!",
                    body: $"""
                    Dear {notification.Contact.Name},

                    {userFullName} wishes you a very happy birthday! 🎂🎈

                    Wishing you a fantastic day filled with joy and surprises!

                    — Your Team
                    """
                );

                TempData["Success"] = "Wishes sent successfully!";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.SendWishes,
                    $"Exception in SendWishes: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Notifications);

                TempData["Error"] = "An error occurred while sending wishes.";
                return RedirectToAction(nameof(Index));
            }
        }
    }
}