﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Controllers\Groups\**" />
    <Content Remove="Controllers\Groups\**" />
    <EmbeddedResource Remove="Controllers\Groups\**" />
    <None Remove="Controllers\Groups\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="bootstrap" Version="5.3.6" />
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="Google.Apis" Version="1.69.0" />
    <PackageReference Include="Google.Apis.PeopleService.v1" Version="1.69.0.3785" />
    <PackageReference Include="MailKit" Version="4.12.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\icons\" />
  </ItemGroup>

</Project>
