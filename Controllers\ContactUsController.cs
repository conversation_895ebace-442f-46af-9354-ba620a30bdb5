﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using AdressBookAppWeb.ViewModels;
using Microsoft.AspNetCore.Mvc;
using System;

namespace AdressBookAppWeb.Controllers
{
    public class ContactUsController : BaseController
    {
        public ContactUsController(AppDbContext context) : base(context)
        {
        }

        [HttpGet]
        public IActionResult Index()
        {
            return View(new ContactUsViewModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Index(ContactUsViewModel model)
        {
            if (!ModelState.IsValid)
                return View(model);

            var userId = HttpContext.Session.GetInt32("UserId");

            try
            {
                var report = new ContactReport
                {
                    SenderName = model.Name,
                    Email = model.Email,
                    Subject = model.Subject,
                    Message = model.Message,
                    UserId = userId,
                    Status = ReportStatus.Pending,
                    ReportDate = DateTime.Now
                };

                _context.ContactReports.Add(report);
                _context.SaveChanges();

                TempData["Success"] = "Your message has been submitted. Thank you!";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.MailSending,
                    $"Exception in ContactUs POST: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Support,
                    userId);

                TempData["Error"] = "An error occurred while sending your message. Please try again later.";
                return View(model);
            }
        }
    }
}
