﻿using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace AdressBookAppWeb.ViewModels
{
    public class UserProfileViewModel
    {
        public int Id { get; set; }

        [Required, EmailAddress]
        public string Email { get; set; }

        [Required]
        public string FirstName { get; set; }

        [Required]
        public string LastName { get; set; }

        [Required]
        public string Phone { get; set; }

        public IFormFile? ProfileImage { get; set; }

        public string? CurrentImagePath { get; set; }
    }
}
