﻿<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <link href="https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/style.min.css" rel="stylesheet" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <link href="~/css/styles.css" rel="stylesheet" />
    <script src="https://use.fontawesome.com/releases/v6.3.0/js/all.js" crossorigin="anonymous"></script>
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#0d6efd">
    <title>@ViewBag.Title</title>
</head>
<body class="sb-nav-fixed">
    <nav class="sb-topnav navbar navbar-expand navbar-dark bg-dark">
        <a class="navbar-brand ps-3" asp-controller="Dashboard" asp-action="Index">Address Book</a>
        <button class="btn btn-link btn-sm order-1 order-lg-0 me-4 me-lg-0" id="sidebarToggle" href="#!">
            <i class="fas fa-bars"></i>
        </button>
        <ul class="navbar-nav ms-auto">
            <li class="nav-item dropdown mx-2 position-relative">
                <a class="nav-link dropdown-toggle" id="notificationDropdown" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-bell"></i>
                </a>
                <span class="position-absolute start-0 translate-middle badge rounded-pill bg-danger"
                      style="font-size: 0.75rem; top: 20%;">
                    @ViewBag.UnreadNotificationCount
                </span>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationDropdown" style="width: 300px;">
                    <li><h6 class="dropdown-header">Notifications</h6></li>

                    @if (ViewBag.UnreadNotificationsTop3 != null && ViewBag.UnreadNotificationsTop3.Count > 0)
                    {
                        foreach (var notification in ViewBag.UnreadNotificationsTop3)
                        {
                            <li>
                                <a class="dropdown-item" href="@Url.Action("Index", "Notifications", new { username = notification.ContactName })">
                                    @notification.Message
                                </a>
                            </li>
                        }
                    }
                    else
                    {
                        <li><span class="dropdown-item text-muted">No unread notifications</span></li>
                    }

                    <li><hr class="dropdown-divider" /></li>
                    <li>
                        <a class="dropdown-item text-center" asp-controller="Notifications" asp-action="Index">
                            View All Notifications
                        </a>
                    </li>
                </ul>
            </li>

            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" id="navbarDropdown" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-user fa-fw"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                    <li><a asp-controller="Profile" asp-action="Index" class="dropdown-item">Profile</a></li>
                    <li><hr class="dropdown-divider" /></li>
                    <li>
                        <a class="dropdown-item" asp-controller="Auth" asp-action="Logout">Logout</a>
                    </li>
                </ul>
            </li>
        </ul>
    </nav>
    <div id="layoutSidenav">
        <div id="layoutSidenav_nav">
            <nav class="sb-sidenav accordion sb-sidenav-dark" id="sidenavAccordion">
                <div class="sb-sidenav-menu">
                    <div class="nav">
                        @* <div class="sb-sidenav-menu-heading">Core</div> *@
                        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Dashboard" ? "active" : "")"
                           asp-controller="Dashboard" asp-action="Index">
                            <div class="sb-nav-link-icon"><i class="fas fa-tachometer-alt"></i></div>
                            Dashboard
                        </a>
                        @* <div class="sb-sidenav-menu-heading">Address Book</div> *@
                        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Contacts" ? "active" : "")"
                           asp-controller="Contacts" asp-action="Index">
                            <div class="sb-nav-link-icon"><i class="fas fa-address-book"></i></div>
                            Contacts
                        </a>
                        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Groups" ? "active" : "")"
                           asp-controller="Groups" asp-action="Index">
                            <div class="sb-nav-link-icon"><i class="fas fa-users"></i></div>
                            Groups
                        </a>

                        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Calendar" ? "active" : "")"
                           asp-controller="Calendar" asp-action="Index">
                            <div class="sb-nav-link-icon"><i class="fas fa-calendar-alt"></i></div>
                            Calendar
                        </a>

                        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "SharedAccess" ? "active" : "")"
                           asp-controller="SharedAccess" asp-action="Index">
                            <div class="sb-nav-link-icon"><i class="fas fa-users"></i></div>
                            Shared Access
                        </a>

                        <div class="sb-sidenav-menu-heading">Settings</div>
                        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Profile" ? "active" : "")"
                           asp-controller="Profile" asp-action="Index">
                            <div class="sb-nav-link-icon"><i class="fas fa-user-cog"></i></div>
                            Profile
                        </a>
                        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "ContactUs" ? "active" : "")"
                           asp-controller="ContactUs" asp-action="Index">
                            <div class="sb-nav-link-icon"><i class="fas fa-envelope"></i></div>
                            Contact Us
                        </a>
                    </div>
                </div>
                <div class="sb-sidenav-footer">
                    <div class="small">Logged in as:</div>
                    @if (ViewBag.CurrentUser != null)
                    {
                        <text>@ViewBag.CurrentUser.FirstName @ViewBag.CurrentUser.LastName</text>
                    }
                    else
                    {
                        Console.WriteLine(ViewBag);
                        <text>Guest</text>
                    }
                </div>
            </nav>
        </div>
        <div id="layoutSidenav_content">
            <main>
                @RenderBody()
            </main>
        </div>
    </div>

    <script>
        function previewImage(event) {
            const reader = new FileReader();
            const imagePreview = document.getElementById("imagePreview");

            reader.onload = function () {
                imagePreview.src = reader.result;
                imagePreview.style.display = "block";
            };

            if (event.target.files[0]) {
                reader.readAsDataURL(event.target.files[0]);
            }
        }

        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/service-worker.js')
                .then(function (registration) {
                    console.log('Service Worker registered with scope:', registration.scope);
                }).catch(function (error) {
                    console.log('Service Worker registration failed:', error);
                });
        }
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"
            crossorigin="anonymous"></script>
    <script src="~/js/scripts.js"></script>
    @* <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.min.js" crossorigin="anonymous"></script> *@
    @* <script src="~/assets/demo/chart-area-demo.js"></script> *@
    @* <script src="~/assets/demo/chart-bar-demo.js"></script> *@
    @* <script src="https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/umd/simple-datatables.min.js" *@
    @*         crossorigin="anonymous"></script> *@
    @* <script src="~/js/datatables-simple-demo.js"></script> *@

    @RenderSection("Scripts", required: false)
</body>
</html>
