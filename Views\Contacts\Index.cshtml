﻿@{
    ViewData["Title"] = "Contacts";
    var contacts = ViewBag.Contacts as List<AdressBookAppWeb.Models.Contact>;
    var groups = ViewBag.Groups as List<AdressBookAppWeb.Models.Group>;
}

<div class="container-fluid px-4">
    <h2 class="mt-4">Contacts</h2>

    @if (ViewBag.Error != null)
    {
        <div class="alert alert-danger">@ViewBag.Error</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <div class="row align-items-start justify-content-between mb-4">
        <div class="col-lg-6 col-md-8 col-sm-12 mb-3 d-flex flex-wrap gap-2">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addContactModal" type="button">Add New Contact</button>
            <a asp-controller="ImportExport" asp-action="Index" class="btn btn-secondary">Import/Export Contacts</a>
            <button class="btn btn-danger" id="bulkDeleteBtn" type="button">Bulk Delete</button>
        </div>

        <div class="col-lg-3 col-md-4 col-sm-12 mb-3 text-end">
            <button class="btn btn-primary w-100" data-bs-toggle="offcanvas" data-bs-target="#searchOffcanvas" aria-controls="searchOffcanvas" type="button">
                Search Contacts
            </button>
        </div>
    </div>

    <div class="row g-3">
        @if (contacts != null && contacts.Any())
        {
            foreach (var contact in contacts)
            {
                <div class="col-sm-6 col-md-4">
                    <div class="card shadow-sm d-flex flex-row align-items-center p-2" style="border-radius: 8px;">
                        <input type="checkbox" class="form-check-input me-2 contact-checkbox" value="@contact.Id" />
                        <img src="@(string.IsNullOrEmpty(contact.ContactImage) ? "/assets/img/default.png" : contact.ContactImage)"
                             class="rounded" style="height: 80px; width: 80px; object-fit: cover;" alt="Contact Image" />
                        <div class="ms-3 w-100">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <h6 class="mb-0">@contact.FirstName @contact.LastName</h6>
                                <button type="button"
                                        class="btn btn-sm toggle-star-btn @(contact.IsStarred ? "btn-warning text-dark" : "btn-outline-warning")"
                                        data-contact-id="@contact.Id">
                                    @(contact.IsStarred ? "★ Starred" : "☆ Star")
                                </button>
                            </div>
                            <p class="mb-0" style="font-size: 0.85rem;">Phone: @contact.Phone</p>
                            <p class="mb-1" style="font-size: 0.85rem;">Email: @contact.Email</p>

                            <div class="d-flex gap-1 mt-2 flex-wrap">
                                <a asp-controller="Contacts" asp-action="ContactDetail" asp-route-id="@contact.Id" class="btn btn-info btn-sm">View</a>
                                <a asp-controller="Contacts" asp-action="EditContact" asp-route-id="@contact.Id" class="btn btn-warning btn-sm">Update</a>
                                <button type="button" class="btn btn-danger btn-sm single-delete-btn" data-contact-id="@contact.Id">Delete</button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12">
                <div class="alert alert-warning text-center">No contacts found.</div>
            </div>
        }
    </div>

    @{
        int currentPage = ViewBag.CurrentPage ?? 1;
        int totalPages = ViewBag.TotalPages ?? 1;
        var filters = ViewBag.QueryParams as Dictionary<string, string?> ?? new();
        string filterQuery = string.Join("&", filters
        .Where(f => !string.IsNullOrEmpty(f.Value))
        .Select(f => $"{f.Key}={f.Value}"));
    }

    <div class="d-flex justify-content-center mt-4">
        <nav aria-label="Contacts Pagination">
            <ul class="pagination">
                @{
                    int visibleRange = 2;
                    int startPage = Math.Max(1, currentPage - visibleRange);
                    int endPage = Math.Min(totalPages, currentPage + visibleRange);
                }

                <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                    <a class="page-link" href="?page=@(currentPage - 1)@("&" + filterQuery)">Previous</a>
                </li>

                @if (startPage > 1)
                {
                    <li class="page-item"><a class="page-link" href="?page=1@("&" + filterQuery)">1</a></li>
                    @if (startPage > 2)
                    {
                        <li class="page-item disabled"><span class="page-link">…</span></li>
                    }
                }

                @for (int i = startPage; i <= endPage; i++)
                {
                    <li class="page-item @(i == currentPage ? "active" : "")">
                        <a class="page-link" href="?page=@i@("&" + filterQuery)">@i</a>
                    </li>
                }

                @if (endPage < totalPages)
                {
                    if (endPage < totalPages - 1)
                    {
                        <li class="page-item disabled"><span class="page-link">…</span></li>
                    }
                    <li class="page-item"><a class="page-link" href="?page=@totalPages@("&" + filterQuery)">@totalPages</a></li>
                }

                <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                    <a class="page-link" href="?page=@(currentPage + 1)@("&" + filterQuery)">Next</a>
                </li>
            </ul>
        </nav>
    </div>
</div>

<div class="modal fade" id="addContactModal" tabindex="-1" aria-labelledby="addContactModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title" id="addContactModalLabel">Add New Contact</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <p>Select how you want to add a new contact (if user has registered on website you can add using user key):</p>
                <div class="d-grid gap-2">
                    <a class="btn btn-primary" asp-controller="Contacts" asp-action="AddContact">Add Manually</a>
                    <a class="btn btn-secondary" asp-controller="Contacts" asp-action="SearchUser">Add Using User Key</a>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="offcanvas offcanvas-end" tabindex="-1" id="searchOffcanvas" aria-labelledby="searchOffcanvasLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="searchOffcanvasLabel">Search & Filter Contacts</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <form id="searchForm" method="get">
            <div class="mb-3">
                <label for="fullnameQuery" class="form-label">Fullname</label>
                <input type="text" class="form-control mb-2" id="fullnameQuery" name="fullnameQuery"
                       placeholder="Enter fullname..." value="@(Context.Request.Query["fullnameQuery"])">
            </div>

            <div class="mb-3">
                <label for="emailQuery" class="form-label">Email</label>
                <input type="email" class="form-control mb-2" id="emailQuery" name="email"
                       placeholder="Enter email..." value="@(Context.Request.Query["email"])">
            </div>

            <div class="mb-3">
                <label for="phoneQuery" class="form-label">Phone Number</label>
                <input type="tel" class="form-control mb-2" id="phoneQuery" name="phoneQuery"
                       placeholder="Enter phone number..." value="@(Context.Request.Query["phoneQuery"])">
            </div>

            <div class="mb-3">
                <label for="groupFilter" class="form-label">Group</label>
                <select class="form-select" id="groupFilter" name="groupId">
                    <option value="">All Groups</option>
                    @if (groups != null)
                    {
                        foreach (var g in groups)
                        {
                            var selected = Context.Request.Query["groupId"].ToString() == g.Id.ToString() ? "selected" : null;
                            <option value="@g.Id" selected="@selected">@g.GroupName</option>
                        }
                    }
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">Birthday Range</label>
                <div class="d-flex gap-2">
                    <input type="date" class="form-control" id="birthdayFrom" name="birthdayFrom"
                           value="@(Context.Request.Query["birthdayFrom"])" placeholder="From">
                    <input type="date" class="form-control" id="birthdayTo" name="birthdayTo"
                           value="@(Context.Request.Query["birthdayTo"])" placeholder="To">
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">Additional Filters</label>
                <div class="form-check">
                    <input type="hidden" name="starred" value="false" />
                    <input class="form-check-input" type="checkbox" value="true" id="starredFilter" name="starred"
                    @(Context.Request.Query["starred"].ToString().ToLower() == "true" ? "checked" : null) />
                    <label class="form-check-label" for="starredFilter">Starred Contacts</label>
                </div>
                <div class="form-check">
                    <input type="hidden" name="recentlyAdded" value="false" />
                    <input class="form-check-input" type="checkbox" value="true" id="recentlyAddedFilter" name="recentlyAdded"
                    @(Context.Request.Query["recentlyAdded"].ToString().ToLower() == "true" ? "checked" : null) />
                    <label class="form-check-label" for="recentlyAddedFilter">Recently Added</label>
                </div>
            </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">Search</button>
                <a href="/Contacts" class="btn btn-secondary">Reset</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <script>
        document.querySelectorAll('.toggle-star-btn').forEach(button => {
            button.addEventListener('click', function () {
                const id = this.dataset.contactId;
                fetch(`/Contacts/ToggleStar/${id}`, { method: 'POST' })
                    .then(res => {
                        if (res.ok) {
                            location.reload();
                        } else {
                            alert("Failed to toggle star status.");
                        }
                    });
            });
        });

        document.querySelectorAll('.single-delete-btn').forEach(button => {
            button.addEventListener('click', function () {
                const id = this.dataset.contactId;
                if (confirm('Are you sure you want to delete this contact?')) {
                    fetch(`/Contacts/Delete/${id}`, { method: 'POST' })
                        .then(res => {
                            if (res.ok) {
                                location.reload();
                            } else {
                                alert("Failed to delete contact.");
                            }
                        });
                }
            });
        });

        document.getElementById('bulkDeleteBtn')?.addEventListener('click', function () {
            const selectedIds = Array.from(document.querySelectorAll('.contact-checkbox:checked'))
                .map(cb => parseInt(cb.value));

            if (selectedIds.length === 0) {
                alert("Please select at least one contact.");
                return;
            }

            if (confirm(`Are you sure you want to delete ${selectedIds.length} selected contacts?`)) {
                fetch('/Contacts/BulkDelete', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(selectedIds)
                })
                .then(res => {
                    if (res.ok) {
                        location.reload();
                    } else {
                        res.text().then(msg => alert(msg || "Bulk delete failed."));
                    }
                });
            }
        });
    </script>
}