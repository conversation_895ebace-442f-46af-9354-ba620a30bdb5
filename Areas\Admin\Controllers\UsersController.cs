﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Helpers;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using AdressBookAppWeb.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AdressBookAppWeb.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class UsersController : BaseController
    {
        private const int PageSize = 10;

        public UsersController(AppDbContext context) : base(context)
        {
        }

        // GET: /Admin/Users
        public async Task<IActionResult> Index(string fullName, string email, string role, int page = 1)
        {
            try
            {
                var query = _context.Users.AsQueryable();

                if (!string.IsNullOrEmpty(fullName))
                    query = query.Where(u => (u.FirstName + " " + u.LastName).Contains(fullName));

                if (!string.IsNullOrEmpty(email))
                    query = query.Where(u => u.Email.Contains(email));

                if (!string.IsNullOrEmpty(role) && Enum.TryParse<UserRole>(role, true, out var parsedRole))
                    query = query.Where(u => u.Role == parsedRole);

                var totalItems = await query.CountAsync();
                var users = await query
                    .OrderByDescending(u => u.RegistrationDate)
                    .Skip((page - 1) * PageSize)
                    .Take(PageSize)
                    .ToListAsync();

                ViewBag.TotalPages = (int)Math.Ceiling(totalItems / (double)PageSize);
                ViewBag.CurrentPage = page;
                ViewBag.Filters = new { fullName, email, role };

                var currentUserId = HttpContext.Session.GetInt32("UserId");
                ViewBag.CurrentUser = await _context.Users.FindAsync(currentUserId);

                return View(users);
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while loading users.";
                return View(Enumerable.Empty<User>());
            }
        }

        // GET: /Admin/Users/<USER>/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null)
                    return NotFound();

                var currentUserId = HttpContext.Session.GetInt32("UserId");
                ViewBag.CurrentUser = await _context.Users.FindAsync(currentUserId);

                return View(user);
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while loading user details.";
                return RedirectToAction("Index");
            }
        }

        // GET: /Admin/Users/<USER>
        [HttpGet]
        public async Task<IActionResult> Add()
        {
            try
            {
                var currentUserId = HttpContext.Session.GetInt32("UserId");
                ViewBag.CurrentUser = await _context.Users.FindAsync(currentUserId);

                var vm = new UserCreateViewModel();
                return View(vm);
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "Unable to load the Add User page.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Add(UserCreateViewModel vm)
        {
            try
            {
                var currentUserId = HttpContext.Session.GetInt32("UserId");
                var currentUser = await _context.Users.FindAsync(currentUserId);


                if (_context.Users.Any(u => u.Email == vm.Email))
                    ModelState.AddModelError("Email", "Email is already registered.");

                if (_context.Users.Any(u => u.Phone == vm.Phone))
                    ModelState.AddModelError("Phone", "Phone number is already in use.");

                if (string.IsNullOrWhiteSpace(vm.Password) || vm.Password.Length < 8)
                {
                    ModelState.AddModelError("Password", "Password must be at least 8 characters long");
                }

                if (!ModelState.IsValid)
                {
                    ViewBag.CurrentUser = currentUser;
                    return View(vm);
                }

                var hashedPassword = PasswordHelper.HashPassword(vm.Password);

                var user = new User
                {
                    FirstName = vm.FirstName,
                    LastName = vm.LastName,
                    Email = vm.Email?.Trim().ToLower(),
                    Phone = vm.Phone?.Trim(),
                    Password = hashedPassword,
                    Role = (currentUser?.Role == UserRole.SuperAdmin) ? vm.Role : UserRole.User,
                    UserKey = Guid.NewGuid().ToString(),
                    RegistrationDate = DateTime.Now
                };

                if (vm.ProfileImage != null && vm.ProfileImage.Length > 0)
                {
                    var uploads = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "images", "profiles");
                    Directory.CreateDirectory(uploads);
                    var filename = Guid.NewGuid().ToString() + Path.GetExtension(vm.ProfileImage.FileName);
                    var path = Path.Combine(uploads, filename);

                    using var stream = new FileStream(path, FileMode.Create);
                    await vm.ProfileImage.CopyToAsync(stream);

                    user.ProfileImage = "/images/profiles/" + filename;
                }

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "User added successfully.";
                return RedirectToAction("Index");
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while creating the user.";
                return RedirectToAction("Add");
            }
        }

        // GET: /Admin/Users/<USER>/5
        [HttpGet]
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var currentUserId = HttpContext.Session.GetInt32("UserId");
                var currentUser = await _context.Users.FindAsync(currentUserId);
                var user = await _context.Users.FindAsync(id);

                if (user == null)
                    return NotFound();

                if (currentUser.Role == UserRole.Admin && user.Role == UserRole.Admin)
                {
                    TempData["ErrorMessage"] = "Admins cannot edit other admins.";
                    return RedirectToAction("Index");
                }

                var vm = new UserEditViewModel
                {
                    Id = user.Id,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Email = user.Email,
                    Phone = user.Phone,
                    Role = user.Role,
                    ProfileImagePath = user.ProfileImage
                };

                ViewBag.CurrentUser = currentUser;
                return View(vm);
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "Unable to load the Edit User page.";
                return RedirectToAction("Index");
            }
        }

        // POST: /Admin/Users/<USER>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(UserEditViewModel vm)
        {
            try
            {
                var currentUserId = HttpContext.Session.GetInt32("UserId");
                var currentUser = await _context.Users.FindAsync(currentUserId);
                var user = await _context.Users.FindAsync(vm.Id);

                if (user == null || currentUser == null)
                    return NotFound();

                if (_context.Users.Any(u => u.Email == vm.Email && u.Id != vm.Id))
                    ModelState.AddModelError("Email", "Email is already registered.");

                if (!string.IsNullOrEmpty(vm.Phone) && _context.Users.Any(u => u.Phone == vm.Phone && u.Id != vm.Id))
                    ModelState.AddModelError("Phone", "Phone number is already in use.");

                if (!ModelState.IsValid)
                {
                    vm.ProfileImagePath = user.ProfileImage;
                    ViewBag.CurrentUser = currentUser;
                    return View(vm);
                }

                // Role assignment logic
                if (currentUser.Id == user.Id || currentUser.Role == UserRole.Admin)
                {
                    // Cannot change role in this case
                    vm.Role = user.Role;
                }
                else if (currentUser.Role == UserRole.SuperAdmin)
                {
                    // SuperAdmin may change role
                    user.Role = vm.Role;
                }

                user.FirstName = vm.FirstName;
                user.LastName = vm.LastName;
                user.Email = vm.Email;
                user.Phone = vm.Phone;

                if (vm.ProfileImage != null && vm.ProfileImage.Length > 0)
                {
                    var uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "images", "profiles");
                    Directory.CreateDirectory(uploadsFolder);

                    var fileName = Guid.NewGuid().ToString() + Path.GetExtension(vm.ProfileImage.FileName);
                    var filePath = Path.Combine(uploadsFolder, fileName);

                    using var stream = new FileStream(filePath, FileMode.Create);
                    await vm.ProfileImage.CopyToAsync(stream);

                    user.ProfileImage = "/images/profiles/" + fileName;
                }

                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "User updated successfully.";
                return RedirectToAction("Index");
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while updating the user.";
                return RedirectToAction("Edit", new { id = vm.Id });
            }
        }

        // POST: /Admin/Users/<USER>/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var currentUserId = HttpContext.Session.GetInt32("UserId");
                var currentUser = await _context.Users.FindAsync(currentUserId);

                var user = await _context.Users
                    .Include(u => u.Groups)
                    .Include(u => u.Contacts)
                    .Include(u => u.OwnedAccesses)
                    .Include(u => u.ViewableAccesses)
                    .FirstOrDefaultAsync(u => u.Id == id);

                if (user == null || currentUser == null)
                    return NotFound();

                // Admins can only delete Role.User
                if (currentUser.Role == UserRole.Admin && user.Role != UserRole.User)
                {
                    TempData["ErrorMessage"] = "Admins can only delete users.";
                    return RedirectToAction("Index");
                }

                // SuperAdmin cannot delete own account
                if (currentUser.Role == UserRole.SuperAdmin && currentUser.Id == user.Id)
                {
                    TempData["ErrorMessage"] = "You cannot delete your own account.";
                    return RedirectToAction("Index");
                }

                // Remove associated data
                if (user.Groups != null && user.Groups.Any())
                    _context.Groups.RemoveRange(user.Groups);

                if (user.Contacts != null && user.Contacts.Any())
                    _context.Contacts.RemoveRange(user.Contacts);

                if (user.OwnedAccesses != null && user.OwnedAccesses.Any())
                    _context.SharedAccesses.RemoveRange(user.OwnedAccesses);

                if (user.ViewableAccesses != null && user.ViewableAccesses.Any())
                    _context.SharedAccesses.RemoveRange(user.ViewableAccesses.Where(a => a.ViewerId == id));

                _context.Users.Remove(user);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "User deleted successfully.";
                return RedirectToAction("Index");
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while deleting the user.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ResetPassword(int id)
        {
            try
            {
                var currentUserId = HttpContext.Session.GetInt32("UserId");
                var currentUser = await _context.Users.FindAsync(currentUserId);
                var user = await _context.Users.FindAsync(id);

                if (user == null)
                    return NotFound();

                // Admins can only reset Role.User
                if (currentUser.Role == UserRole.Admin && user.Role != UserRole.User)
                {
                    return Forbid();
                }

                // Generate a temporary password
                var newPassword = Guid.NewGuid().ToString("N").Substring(0, 10);

                // Hash the password before saving
                user.Password = PasswordHelper.HashPassword(newPassword);
                await _context.SaveChangesAsync();

                // Show plain password only once to the admin
                TempData["SuccessMessage"] = $"Password reset. New password: {newPassword}";
                return RedirectToAction("Index");
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while resetting the password.";
                return RedirectToAction("Index");
            }
        }

    }
}