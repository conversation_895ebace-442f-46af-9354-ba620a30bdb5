﻿using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;

[Route("Error")]
public class ErrorController : Controller
{
    [Route("400")]
    public IActionResult BadRequestError()
    {
        var statusCodeReExecuteFeature = HttpContext.Features.Get<IStatusCodeReExecuteFeature>();

        ViewBag.ErrorMessage = TempData["ErrorMessage"] as string ?? "Bad Request";
        ViewBag.OriginalPath = statusCodeReExecuteFeature?.OriginalPath;
        ViewBag.OriginalQueryString = statusCodeReExecuteFeature?.OriginalQueryString;

        Response.StatusCode = 400;
        return View("400");
    }
    [Route("401")]
    public IActionResult UnauthorizedAccess() => View("401");

    [Route("403")]
    public IActionResult Forbidden() => View("403");

    [Route("404")]
    public IActionResult NotFoundPage() => View("404");

    [Route("500")]
    public IActionResult ServerError()
    {
        var exceptionFeature = HttpContext.Features.Get<IExceptionHandlerFeature>();
        ViewBag.ErrorMessage = exceptionFeature?.Error.Message;
        ViewBag.StackTrace = exceptionFeature?.Error.StackTrace;

        Response.StatusCode = 500;
        return View("500");
    }
}