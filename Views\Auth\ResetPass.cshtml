﻿@{
    Layout = "_AuthLayout";
    ViewData["Title"] = "Reset Password";
}

<div class="col-lg-5">
    <div class="card shadow-lg border-0 rounded-lg mt-5">
        <div class="card-header"><h3 class="text-center font-weight-light my-4">Password Recovery</h3></div>
        <div class="card-body">
            <div class="small mb-3 text-muted">Enter your email address and we will send you a link to reset your password.</div>
            <form asp-action="ResetPass" method="post">
                <div class="form-floating mb-3">
                    <input class="form-control" name="email" type="email" placeholder="<EMAIL>" required />
                    <label for="inputEmail">Email address</label>
                </div>
                <div class="d-flex justify-content-between mt-4 mb-0">
                    <a class="small" asp-controller="Auth" asp-action="Login">Return to login</a>
                    <button class="btn btn-primary" type="submit">Reset Password</button>
                </div>
            </form>
        </div>
        <div class="card-footer text-center py-3">
            <div class="small">
                <a asp-controller="Auth" asp-action="Register">Need an account? Sign up!</a>
            </div>
            <div class="small mt-2">
                <a asp-controller="Auth" asp-action="Support">Having trouble? Contact Support</a>
            </div>
        </div>
    </div>
</div>