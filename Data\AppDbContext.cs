﻿using Microsoft.EntityFrameworkCore;
using AdressBookAppWeb.Models;

namespace AdressBookAppWeb.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options) { }

        public DbSet<User> Users { get; set; }
        public DbSet<Contact> Contacts { get; set; }
        public DbSet<Group> Groups { get; set; }
        public DbSet<SharedAccess> SharedAccesses { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<Log> Logs { get; set; }
        public DbSet<ContactReport> ContactReports { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<User>()
                .HasIndex(u => u.Email)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasIndex(u => u.Phone)
                .IsUnique();

            // User - Contact (1:M)
            modelBuilder.Entity<Contact>()
                .HasOne(c => c.User)
                .WithMany(u => u.Contacts)
                .HasForeignKey(c => c.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Contact - Group (M:1)
            modelBuilder.Entity<Contact>()
                .HasOne(c => c.Group)
                .WithMany(g => g.Contacts)
                .HasForeignKey(c => c.GroupId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Contact>()
                .HasIndex(c => new { c.UserId, c.CreatedDate })
                .HasDatabaseName("IX_Contacts_UserId_CreatedDate");

            // User - Group (1:M)
            modelBuilder.Entity<Group>()
                .HasOne(g => g.User)
                .WithMany(u => u.Groups)
                .HasForeignKey(g => g.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // SharedAccess: Owner -> User (M:1)
            modelBuilder.Entity<SharedAccess>()
                .HasOne(s => s.Owner)
                .WithMany(u => u.OwnedAccesses)
                .HasForeignKey(s => s.OwnerId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SharedAccess>()
                .HasOne(s => s.Viewer)
                .WithMany(u => u.ViewableAccesses)
                .HasForeignKey(s => s.ViewerId)
                .OnDelete(DeleteBehavior.Restrict); 

            // Notification - User (M:1)
            modelBuilder.Entity<Notification>()
                .HasOne(n => n.User)
                .WithMany(u => u.Notifications)
                .HasForeignKey(n => n.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Notification - Contact (M:1)
            modelBuilder.Entity<Notification>()
                .HasOne(n => n.Contact)
                .WithMany(c => c.Notifications)
                .HasForeignKey(n => n.ContactId)
                .OnDelete(DeleteBehavior.SetNull);

            // Log - User (M:1)
            modelBuilder.Entity<Log>()
                .HasOne(l => l.User)
                .WithMany(u => u.Logs)
                .HasForeignKey(l => l.UserId)
                .OnDelete(DeleteBehavior.SetNull);

            // ContactReport - User (M:1)
            modelBuilder.Entity<ContactReport>()
                .HasOne(cr => cr.User)
                .WithMany(u => u.ContactReports)
                .HasForeignKey(cr => cr.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}