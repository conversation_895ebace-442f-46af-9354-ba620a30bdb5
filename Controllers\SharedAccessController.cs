﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AdressBookAppWeb.Controllers
{
    public class SharedAccessController : BaseController
    {
        private const int ManagePageSize = 6;
        private const int ContactsPageSize = 9;

        public SharedAccessController(AppDbContext context) : base(context)
        {
        }

        [HttpGet]
        public IActionResult Index(string searchTerm, string accessStatus, int page = 1)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to manage shared access.";
                    return RedirectToAction("Login", "Auth");
                }

                var query = _context.SharedAccesses
                    .Include(s => s.Viewer)
                    .Where(s => s.OwnerId == userId.Value)
                    .AsQueryable();

                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = query.Where(s =>
                        (s.Viewer.FirstName + " " + s.Viewer.LastName).Contains(searchTerm) ||
                        s.Viewer.Email.Contains(searchTerm));
                }

                if (!string.IsNullOrWhiteSpace(accessStatus) &&
                    Enum.TryParse<AccessStatus>(accessStatus, true, out var parsedStatus))
                {
                    query = query.Where(s => s.AccessStatus == parsedStatus);
                }

                int totalItems = query.Count();
                var items = query
                    .OrderBy(s => s.Viewer.FirstName)
                    .ThenBy(s => s.Viewer.LastName)
                    .Skip((page - 1) * ManagePageSize)
                    .Take(ManagePageSize)
                    .ToList();

                ViewBag.CurrentPage = page;
                ViewBag.TotalPages = (int)Math.Ceiling(totalItems / (double)ManagePageSize);
                ViewBag.SearchTerm = searchTerm;
                ViewBag.AccessStatus = accessStatus;

                return View(items);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ViewSharedAccess,
                    $"Exception in Index: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "An error occurred while loading shared access.";
                return View(new List<SharedAccess>());
            }
        }

        [HttpGet]
        public IActionResult ContactsSharedWithMe(string searchTerm, int page = 1)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to view contacts shared with you.";
                    return RedirectToAction("Login", "Auth");
                }

                var query = _context.SharedAccesses
                    .Include(s => s.Owner)
                    .Where(s => s.ViewerId == userId.Value && s.AccessStatus == AccessStatus.Accessed)
                    .AsQueryable();

                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = query.Where(s =>
                        (s.Owner.FirstName + " " + s.Owner.LastName).Contains(searchTerm) ||
                        s.Owner.Email.Contains(searchTerm));
                }

                int totalItems = query.Count();
                var items = query
                    .OrderBy(s => s.Owner.FirstName)
                    .ThenBy(s => s.Owner.LastName)
                    .Skip((page - 1) * ManagePageSize)
                    .Take(ManagePageSize)
                    .ToList();

                ViewBag.CurrentPage = page;
                ViewBag.TotalPages = (int)Math.Ceiling(totalItems / (double)ManagePageSize);
                ViewBag.SearchTerm = searchTerm;

                return View(items);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ViewSharedWithMe,
                    $"Exception in ContactsSharedWithMe: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "An error occurred while loading contacts shared with you.";
                return View(new List<SharedAccess>());
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Grant(int id)
        {
            try
            {
                var sharedAccess = await _context.SharedAccesses.FindAsync(id);
                if (sharedAccess == null)
                {
                    TempData["Error"] = "Shared access record not found.";
                    return RedirectToAction(nameof(Index));
                }

                sharedAccess.AccessStatus = AccessStatus.Accessed;
                sharedAccess.GrantedDate = DateTime.Now;
                sharedAccess.RevokedDate = null;

                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.GrantAccess,
                    $"Exception in Grant: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "An error occurred while granting access.";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Revoke(int id)
        {
            try
            {
                var sharedAccess = await _context.SharedAccesses.FindAsync(id);
                if (sharedAccess == null)
                {
                    TempData["Error"] = "Shared access record not found.";
                    return RedirectToAction(nameof(Index));
                }

                sharedAccess.AccessStatus = AccessStatus.Revoked;
                sharedAccess.RevokedDate = DateTime.Now;

                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.RevokeAccess,
                    $"Exception in Revoke: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "An error occurred while revoking access.";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Remove(int id)
        {
            try
            {
                var sharedAccess = await _context.SharedAccesses.FindAsync(id);
                if (sharedAccess == null)
                {
                    TempData["Error"] = "Shared access record not found.";
                    return RedirectToAction(nameof(Index));
                }

                _context.SharedAccesses.Remove(sharedAccess);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.RemoveAccess,
                    $"Exception in Remove: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "An error occurred while removing access.";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpGet]
        public IActionResult Contacts(
            int ownerId,
            string fullnameQuery,
            string email,
            string phone,
            int? groupId,
            DateTime? birthdayFrom,
            DateTime? birthdayTo,
            int page = 1)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to view shared contacts.";
                    return RedirectToAction("Login", "Auth");
                }

                var accessRecord = _context.SharedAccesses.FirstOrDefault(sa =>
                    sa.OwnerId == ownerId &&
                    sa.ViewerId == userId.Value &&
                    sa.AccessStatus == AccessStatus.Accessed);

                if (accessRecord == null)
                {
                    TempData["Error"] = "You do not have permission to view these contacts.";
                    return RedirectToAction("UnauthorizedAccess", "Error");
                }

                var query = _context.Contacts
                    .Include(c => c.Group)
                    .Where(c => c.UserId == ownerId)
                    .AsQueryable();

                if (!string.IsNullOrWhiteSpace(fullnameQuery))
                {
                    query = query.Where(c => (c.FirstName + " " + c.LastName).Contains(fullnameQuery));
                }

                if (!string.IsNullOrWhiteSpace(email))
                {
                    query = query.Where(c => c.Email.Contains(email));
                }

                if (!string.IsNullOrWhiteSpace(phone))
                {
                    query = query.Where(c => c.Phone.Contains(phone));
                }

                if (groupId.HasValue)
                {
                    query = query.Where(c => c.GroupId == groupId.Value);
                }

                if (birthdayFrom.HasValue)
                {
                    query = query.Where(c => c.BirthDate >= birthdayFrom.Value);
                }

                if (birthdayTo.HasValue)
                {
                    query = query.Where(c => c.BirthDate <= birthdayTo.Value);
                }

                int totalItems = query.Count();
                var contacts = query
                    .OrderBy(c => c.FirstName)
                    .ThenBy(c => c.LastName)
                    .Skip((page - 1) * ContactsPageSize)
                    .Take(ContactsPageSize)
                    .ToList();

                var groups = _context.Groups
                    .Where(g => g.UserId == ownerId)
                    .OrderBy(g => g.GroupName)
                    .ToList();

                ViewBag.OwnerId = ownerId;
                ViewBag.CurrentPage = page;
                ViewBag.TotalPages = (int)Math.Ceiling(totalItems / (double)ContactsPageSize);
                ViewBag.Groups = groups;

                ViewBag.QueryParams = new Dictionary<string, string?>
                {
                    ["fullnameQuery"] = fullnameQuery,
                    ["email"] = email,
                    ["phone"] = phone,
                    ["groupId"] = groupId?.ToString(),
                    ["birthdayFrom"] = birthdayFrom?.ToString("yyyy-MM-dd"),
                    ["birthdayTo"] = birthdayTo?.ToString("yyyy-MM-dd")
                };

                return View(contacts);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ViewSharedContacts,
                    $"Exception in Contacts: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "An error occurred while loading shared contacts.";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpGet]
        public IActionResult ContactDetails(int id)
        {
            try
            {
                var contact = _context.Contacts
                    .Include(c => c.Group)
                    .Include(c => c.User)
                    .FirstOrDefault(c => c.Id == id);

                if (contact == null)
                {
                    TempData["Error"] = "Contact not found.";
                    return RedirectToAction(nameof(Index));
                }

                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to view contact details.";
                    return RedirectToAction("Login", "Auth");
                }

                var access = _context.SharedAccesses.FirstOrDefault(sa =>
                    sa.OwnerId == contact.UserId &&
                    sa.ViewerId == userId.Value &&
                    sa.AccessStatus == AccessStatus.Accessed);

                if (access == null)
                {
                    TempData["Error"] = "You do not have permission to view this contact's details.";
                    return RedirectToAction("UnauthorizedAccess", "Error");
                }

                return View(contact);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ViewSharedContactDetails,
                    $"Exception in ContactDetails: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "An error occurred while loading contact details.";
                return RedirectToAction(nameof(Index));
            }
        }

        // Helper to log activity
        private void LogActivity(LogAction action, string message, LogStatus status, LogModule module, int? userId = null)
        {
            try
            {
                _context.Logs.Add(new Log
                {
                    Action = action,
                    Module = module,
                    Message = message,
                    Status = status,
                    Timestamp = DateTime.Now,
                    IPAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "N/A",
                    UserId = userId
                });
                _context.SaveChanges();
            }
            catch
            {
                // Swallow logging errors
            }
        }
    }
}