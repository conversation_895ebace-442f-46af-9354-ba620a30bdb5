﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using AdressBookAppWeb.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;

namespace AdressBookAppWeb.Controllers
{
    public class GroupsController : BaseController
    {
        public GroupsController(AppDbContext context) : base(context)
        {
        }

        [HttpGet]
        public IActionResult Index(string? groupName, string? description, int? minContacts)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to manage your groups.";
                    return RedirectToAction("Login", "Auth");
                }

                var groupsQuery = _context.Groups
                    .Include(g => g.Contacts)
                    .Where(g => g.UserId == userId.Value)
                    .AsQueryable();

                if (!string.IsNullOrWhiteSpace(groupName))
                {
                    groupsQuery = groupsQuery.Where(g => g.GroupName.Contains(groupName));
                }

                if (!string.IsNullOrWhiteSpace(description))
                {
                    groupsQuery = groupsQuery.Where(g => g.GroupDescription.Contains(description));
                }

                if (minContacts.HasValue)
                {
                    groupsQuery = groupsQuery.Where(g => g.Contacts.Count >= minContacts.Value);
                }

                var groups = groupsQuery.ToList();
                return View(groups);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ViewGroups,
                    $"Exception in Index: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Groups);

                TempData["Error"] = "An error occurred while loading groups.";
                return View(new List<Group>());
            }
        }

        [HttpPost]
        [ActionName("Filter")]
        [ValidateAntiForgeryToken]
        public IActionResult FilterGroups(string? groupName, string? description, int? minContacts)
        {
            // No DB work here, just redirect to GET Index with query params
            return RedirectToAction("Index", new
            {
                groupName,
                description,
                minContacts
            });
        }

        [HttpGet]
        public IActionResult AddGroup()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to create a group.";
                    return RedirectToAction("Login", "Auth");
                }

                return View();
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.AddGroup,
                    $"Exception in AddGroup GET: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Groups);

                TempData["Error"] = "Unable to load the create‐group page.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult AddGroup(AddGroupViewModel model)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
            {
                TempData["Error"] = "Please log in to create a group.";
                return RedirectToAction("Login", "Auth");
            }

            try
            {
                // Check for duplicate name
                if (_context.Groups.Any(g => g.UserId == userId.Value && g.GroupName == model.GroupName))
                {
                    ModelState.AddModelError("GroupName", "A group with this name already exists.");
                }

                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                var group = new Group
                {
                    GroupName = model.GroupName,
                    GroupDescription = model.GroupDescription,
                    UserId = userId.Value,
                    CreatedDate = DateTime.Now
                };

                _context.Groups.Add(group);
                _context.SaveChanges();

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.AddGroup,
                    $"Exception in AddGroup POST: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Groups,
                    userId.Value);

                TempData["Error"] = "An error occurred while creating the group.";
                return View(model);
            }
        }

        [HttpGet]
        public IActionResult EditGroup(int id)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to edit a group.";
                    return RedirectToAction("Login", "Auth");
                }

                var group = _context.Groups.FirstOrDefault(g => g.Id == id && g.UserId == userId.Value);
                if (group == null)
                {
                    TempData["Error"] = "Group not found.";
                    return RedirectToAction("Index");
                }

                var model = new EditGroupViewModel
                {
                    Id = group.Id,
                    GroupName = group.GroupName,
                    GroupDescription = group.GroupDescription
                };
                return View(model);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.EditGroup,
                    $"Exception in EditGroup GET: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Groups);

                TempData["Error"] = "Unable to load the edit page.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult EditGroup(EditGroupViewModel model)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
            {
                TempData["Error"] = "Please log in to edit a group.";
                return RedirectToAction("Login", "Auth");
            }

            if (!ModelState.IsValid)
                return View(model);

            try
            {
                var group = _context.Groups.FirstOrDefault(g => g.Id == model.Id && g.UserId == userId.Value);
                if (group == null)
                {
                    TempData["Error"] = "Group not found.";
                    return RedirectToAction("Index");
                }

                // Check for duplicate name (excluding this group)
                if (_context.Groups.Any(g => g.UserId == userId.Value && g.GroupName == model.GroupName && g.Id != model.Id))
                {
                    ModelState.AddModelError("GroupName", "A group with this name already exists.");
                    return View(model);
                }

                group.GroupName = model.GroupName;
                group.GroupDescription = model.GroupDescription;
                group.UpdatedDate = DateTime.Now;

                _context.SaveChanges();
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.EditGroup,
                    $"Exception in EditGroup POST: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Groups,
                    userId.Value);

                TempData["Error"] = "An error occurred while updating the group.";
                return View(model);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult DeleteGroup([FromBody] Dictionary<string, int> data)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
            {
                return Json(new { success = false, message = "Unauthorized." });
            }

            try
            {
                if (!data.TryGetValue("id", out var id))
                {
                    return Json(new { success = false, message = "Invalid request." });
                }

                var group = _context.Groups
                    .Include(g => g.Contacts)
                    .FirstOrDefault(g => g.Id == id && g.UserId == userId.Value);

                if (group == null)
                {
                    return Json(new { success = false, message = "Group not found." });
                }

                // Remove associated contacts first
                if (group.Contacts != null && group.Contacts.Any())
                {
                    _context.Contacts.RemoveRange(group.Contacts);
                }

                _context.Groups.Remove(group);
                _context.SaveChanges();

                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.DeleteGroup,
                    $"Exception in DeleteGroup: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Groups,
                    userId.Value);

                return Json(new { success = false, message = "An error occurred while deleting the group." });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult DeleteSelectedGroups([FromBody] List<int> groupIds)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
                return Json(new { success = false, message = "Unauthorized." });

            if (groupIds == null || !groupIds.Any())
                return Json(new { success = false, message = "No groups selected." });

            try
            {
                var groups = _context.Groups
                    .Include(g => g.Contacts)
                    .Where(g => groupIds.Contains(g.Id) && g.UserId == userId.Value)
                    .ToList();

                if (!groups.Any())
                    return Json(new { success = false, message = "No matching groups found." });

                foreach (var group in groups)
                {
                    if (group.Contacts != null && group.Contacts.Any())
                    {
                        _context.Contacts.RemoveRange(group.Contacts);
                    }
                    _context.Groups.Remove(group);
                }

                _context.SaveChanges();

                LogActivity(
                    LogAction.BulkDelete,
                    $"Deleted {groups.Count} group(s) and their associated contacts.",
                    LogStatus.Successful,
                    LogModule.Groups,
                    userId.Value);

                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.BulkDelete,
                    $"Exception during group bulk delete: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Groups,
                    userId.Value);

                return Json(new { success = false, message = "An error occurred while deleting groups." });
            }
        }
    }
}