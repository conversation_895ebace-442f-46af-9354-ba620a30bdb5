﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace AdressBookAppWeb.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class LogsController : BaseController
    {
        private const int PageSize = 10;

        public LogsController(AppDbContext context) : base(context)
        {
        }

        // GET: /Admin/Logs
        public async Task<IActionResult> Index(string module, string actionType, string status, string username, DateTime? dateFrom, DateTime? dateTo, int page = 1)
        {
            try
            {
                var query = _context.Logs
                    .Include(l => l.User)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(module) && Enum.TryParse<LogModule>(module, out var parsedModule))
                {
                    query = query.Where(l => l.Module == parsedModule);
                }

                if (!string.IsNullOrEmpty(actionType) && Enum.TryParse<LogAction>(actionType, out var parsedAction))
                {
                    query = query.Where(l => l.Action == parsedAction);
                }

                if (!string.IsNullOrEmpty(status) && Enum.TryParse<LogStatus>(status, out var parsedStatus))
                {
                    query = query.Where(l => l.Status == parsedStatus);
                }

                if (!string.IsNullOrWhiteSpace(username))
                {
                    query = query.Where(l =>
                        (l.User.FirstName + " " + l.User.LastName).Contains(username) ||
                        l.User.Email.Contains(username));
                }

                if (dateFrom.HasValue)
                {
                    query = query.Where(l => l.Timestamp.Date >= dateFrom.Value.Date);
                }

                if (dateTo.HasValue)
                {
                    query = query.Where(l => l.Timestamp.Date <= dateTo.Value.Date);
                }

                var totalItems = await query.CountAsync();
                var logs = await query
                    .OrderByDescending(l => l.Timestamp)
                    .Skip((page - 1) * PageSize)
                    .Take(PageSize)
                    .ToListAsync();

                ViewBag.TotalPages = (int)Math.Ceiling(totalItems / (double)PageSize);
                ViewBag.CurrentPage = page;
                ViewBag.Filters = new { module, actionType, status, username, dateFrom, dateTo };

                return View(logs);
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while loading activity logs.";
                return View(Enumerable.Empty<Log>());
            }
        }

        // GET: /Admin/Logs/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var log = await _context.Logs
                    .Include(l => l.User)
                    .FirstOrDefaultAsync(l => l.Id == id);

                if (log == null)
                    return NotFound();

                return View(log);
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while loading the log details.";
                return RedirectToAction("Index");
            }
        }
    }
}