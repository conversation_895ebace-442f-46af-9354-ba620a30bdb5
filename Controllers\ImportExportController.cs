﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using ClosedXML.Excel;
using Google.Apis.Auth.OAuth2;
using Google.Apis.PeopleService.v1;
using Google.Apis.Services;
using Google.Apis.PeopleService.v1.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;

namespace AdressBookAppWeb.Controllers
{
    public class ImportExportController : BaseController
    {
        private readonly IConfiguration _configuration;

        public ImportExportController(AppDbContext context, IConfiguration configuration) : base(context)
        {
            _configuration = configuration;
        }

        [HttpGet]
        public IActionResult Index()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to import or export contacts.";
                    return RedirectToAction("Login", "Auth");
                }

                return View();
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ViewImportExport,
                    $"Exception in ImportExport Index: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "Unable to load import/export page.";
                return View();
            }
        }

        // ----------------- GOOGLE CONTACTS -----------------
        [HttpGet]
        public IActionResult ImportFromGoogle()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to import from Google Contacts.";
                    return RedirectToAction("Login", "Auth");
                }

                var config = GetGoogleConfig();
                var queryParams = new Dictionary<string, string>
                {
                    ["client_id"] = config.ClientId,
                    ["redirect_uri"] = config.RedirectUri,
                    ["response_type"] = "code",
                    ["scope"] = "https://www.googleapis.com/auth/contacts.readonly",
                    ["access_type"] = "offline",
                    ["prompt"] = "consent"
                };

                var query = string.Join("&", queryParams.Select(kv => $"{kv.Key}={HttpUtility.UrlEncode(kv.Value)}"));
                return Redirect($"https://accounts.google.com/o/oauth2/v2/auth?{query}");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.GoogleImport,
                    $"Exception in ImportFromGoogle GET: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "Unable to initiate Google import.";
                return RedirectToAction("Index");
            }
        }

        [HttpGet]
        public async Task<IActionResult> GoogleCallback(string code)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
            {
                TempData["Error"] = "Please log in to complete Google import.";
                return RedirectToAction("Login", "Auth");
            }

            if (string.IsNullOrWhiteSpace(code))
            {
                TempData["Error"] = "No authorization code was returned from Google.";
                return RedirectToAction("Index");
            }

            var config = GetGoogleConfig();
            TokenResponse? token;
            try
            {
                token = await ExchangeCodeForToken(code, config.ClientId, config.ClientSecret, config.RedirectUri);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.GoogleImport,
                    $"Exception during token exchange: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts,
                    userId);
                TempData["Error"] = "Failed to exchange authorization code for token.";
                return RedirectToAction("Index");
            }

            if (token == null)
            {
                LogActivity(
                    LogAction.GoogleImport,
                    "Token exchange returned null.",
                    LogStatus.Error,
                    LogModule.Contacts,
                    userId);

                TempData["Error"] = "Failed to exchange token.";
                return RedirectToAction("Index");
            }

            try
            {
                var credential = GoogleCredential
                    .FromAccessToken(token.AccessToken)
                    .CreateScoped(PeopleServiceService.Scope.ContactsReadonly);

                var service = new PeopleServiceService(new BaseClientService.Initializer
                {
                    HttpClientInitializer = credential,
                    ApplicationName = "Address Book Web Client"
                });

                var request = service.People.Connections.List("people/me");
                request.PersonFields = "names,emailAddresses,phoneNumbers,addresses,birthdays,organizations,photos";
                request.PageSize = 2000;

                var response = await request.ExecuteAsync();
                int imported = 0;

                foreach (var person in response.Connections ?? new List<Person>())
                {
                    var nameInfo = person.Names?.FirstOrDefault();
                    var firstName = nameInfo?.GivenName?.Trim();
                    var lastName = nameInfo?.FamilyName?.Trim();

                    var phone = person.PhoneNumbers?.FirstOrDefault()?.Value;
                    var email = person.EmailAddresses?.FirstOrDefault()?.Value;
                    var address = person.Addresses?.FirstOrDefault()?.FormattedValue;
                    var birthday = person.Birthdays?.FirstOrDefault()?.Date;

                    DateTime? birthDate = null;
                    if (birthday != null &&
                        birthday.Year.HasValue &&
                        birthday.Month.HasValue &&
                        birthday.Day.HasValue)
                    {
                        try
                        {
                            birthDate = new DateTime(birthday.Year.Value, birthday.Month.Value, birthday.Day.Value);
                        }
                        catch
                        {
                            birthDate = null;
                        }
                    }

                    // Skip entirely blank contacts
                    if (string.IsNullOrWhiteSpace(firstName) &&
                        string.IsNullOrWhiteSpace(lastName) &&
                        string.IsNullOrWhiteSpace(phone) &&
                        string.IsNullOrWhiteSpace(email))
                    {
                        continue;
                    }

                    var contact = new Contact
                    {
                        UserId = userId.Value,
                        FirstName = string.IsNullOrWhiteSpace(firstName) ? "Unnamed" : firstName,
                        LastName = lastName,
                        Phone = phone,
                        Email = email,
                        Address = address,
                        Company = person.Organizations?.FirstOrDefault()?.Name,
                        BirthDate = birthDate,
                        CreatedDate = DateTime.UtcNow
                    };

                    _context.Contacts.Add(contact);
                    imported++;
                }

                await _context.SaveChangesAsync();

                LogActivity(
                    LogAction.GoogleImport,
                    $"Imported {imported} contact(s) from Google.",
                    LogStatus.Successful,
                    LogModule.Contacts,
                    userId);

                TempData["Message"] = $"{imported} contact(s) imported from Google.";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.GoogleImport,
                    $"Exception during Google import: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts,
                    userId);

                TempData["Error"] = "An error occurred while importing from Google.";
                return RedirectToAction("Index");
            }
        }

        private async Task<TokenResponse?> ExchangeCodeForToken(
            string code,
            string clientId,
            string clientSecret,
            string redirectUri)
        {
            using var httpClient = new HttpClient();
            var parameters = new Dictionary<string, string>
            {
                { "code", code },
                { "client_id", clientId },
                { "client_secret", clientSecret },
                { "redirect_uri", redirectUri },
                { "grant_type", "authorization_code" }
            };

            var response = await httpClient.PostAsync(
                "https://oauth2.googleapis.com/token",
                new FormUrlEncodedContent(parameters));

            if (!response.IsSuccessStatusCode)
                return null;

            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<TokenResponse>(json);
        }

        private (string ClientId, string ClientSecret, string RedirectUri) GetGoogleConfig()
        {
            var cfg = _configuration.GetSection("GoogleOAuth");
            return (
                cfg["ClientId"] ?? throw new Exception("Missing Google ClientId"),
                cfg["ClientSecret"] ?? throw new Exception("Missing Google ClientSecret"),
                cfg["RedirectUri"] ?? throw new Exception("Missing Google RedirectUri")
            );
        }

        private class TokenResponse
        {
            [JsonPropertyName("access_token")]
            public string AccessToken { get; set; }

            [JsonPropertyName("token_type")]
            public string TokenType { get; set; }

            [JsonPropertyName("expires_in")]
            public int ExpiresIn { get; set; }

            [JsonPropertyName("scope")]
            public string Scope { get; set; }
        }

        /*-----------------  EXPORT TO VCF  ------------------------*/
        [HttpGet]
        public IActionResult ExportToVcf()
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
            {
                TempData["Error"] = "Please log in to export contacts.";
                return RedirectToAction("Login", "Auth");
            }

            try
            {
                var contacts = _context.Contacts
                    .Where(c => c.UserId == userId.Value)
                    .ToList();

                var sb = new StringBuilder();
                foreach (var c in contacts)
                {
                    sb.AppendLine("BEGIN:VCARD");
                    sb.AppendLine("VERSION:3.0");

                    string fullName = $"{c.FirstName} {c.LastName}".Trim();
                    sb.AppendLine($"FN:{fullName}");
                    sb.AppendLine($"N:{c.LastName};{c.FirstName};;;");

                    if (!string.IsNullOrWhiteSpace(c.Phone))
                        sb.AppendLine($"TEL;TYPE=CELL:{c.Phone}");
                    if (!string.IsNullOrWhiteSpace(c.Email))
                        sb.AppendLine($"EMAIL:{c.Email}");
                    if (c.BirthDate.HasValue)
                        sb.AppendLine($"BDAY:{c.BirthDate:yyyy-MM-dd}");
                    if (!string.IsNullOrWhiteSpace(c.Address))
                        sb.AppendLine($"ADR;TYPE=HOME:;;{c.Address}");
                    if (!string.IsNullOrWhiteSpace(c.Company))
                        sb.AppendLine($"ORG:{c.Company}");
                    if (!string.IsNullOrWhiteSpace(c.Tags))
                        sb.AppendLine($"NOTE:{c.Tags}");

                    if (!string.IsNullOrWhiteSpace(c.ContactImage))
                    {
                        var imgPath = Path.Combine("wwwroot", c.ContactImage.TrimStart('/'));
                        if (System.IO.File.Exists(imgPath))
                        {
                            var jpgBytes = System.IO.File.ReadAllBytes(imgPath);
                            sb.AppendLine($"PHOTO;ENCODING=b;TYPE=JPEG:{Convert.ToBase64String(jpgBytes)}");
                        }
                    }

                    sb.AppendLine("END:VCARD");
                }

                var vcfBytes = Encoding.UTF8.GetBytes(sb.ToString());

                LogActivity(
                    LogAction.VCFExport,
                    $"Exported {contacts.Count} contacts to VCF.",
                    LogStatus.Successful,
                    LogModule.Contacts,
                    userId);

                return File(
                    vcfBytes,
                    "text/vcard",
                    "contacts_export.vcf");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.VCFExport,
                    $"Exception during VCF export: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts,
                    userId);

                TempData["Error"] = "An error occurred while exporting contacts to VCF.";
                return RedirectToAction("Index");
            }
        }

        /*-----------------  IMPORT FROM VCF  ------------------------*/
        [HttpGet]
        public IActionResult ImportFromVcf()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to import from VCF.";
                    return RedirectToAction("Login", "Auth");
                }

                return View();
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.VCFImport,
                    $"Exception in ImportFromVcf GET: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "Unable to load VCF import page.";
                return View();
            }
        }

        [HttpPost]
        public async Task<IActionResult> ImportFromVcf(IFormFile vcfFile)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
            {
                TempData["Error"] = "Please log in to import from VCF.";
                return RedirectToAction("Login", "Auth");
            }

            if (vcfFile == null || vcfFile.Length == 0)
            {
                TempData["Error"] = "Please upload a valid VCF file.";
                return View();
            }

            try
            {
                using var reader = new StreamReader(vcfFile.OpenReadStream(), Encoding.UTF8);
                var allText = await reader.ReadToEndAsync();
                int skipped = 0;
                var contacts = ParseVcfContacts(allText, userId.Value, ref skipped);

                if (contacts.Any())
                    _context.Contacts.AddRange(contacts);

                await _context.SaveChangesAsync();

                LogActivity(
                    LogAction.VCFImport,
                    $"Imported {contacts.Count} contacts from VCF ({skipped} skipped).",
                    LogStatus.Successful,
                    LogModule.Contacts,
                    userId);

                TempData["Message"] = $"{contacts.Count} contact(s) imported.";
                if (skipped > 0)
                    TempData["Warning"] = $"{skipped} card(s) were skipped (no usable data).";

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.VCFImport,
                    $"Exception during VCF import: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts,
                    userId);

                TempData["Error"] = "An error occurred while importing VCF file.";
                return View();
            }
        }

        /*-----------  VCF Parsing helpers  -----------------*/
        private static List<Contact> ParseVcfContacts(string vcf, int userId, ref int skipped)
        {
            var list = new List<Contact>();
            var cards = Regex.Matches(
                vcf,
                @"BEGIN:VCARD(.*?)END:VCARD",
                RegexOptions.Singleline | RegexOptions.IgnoreCase);

            foreach (Match m in cards)
            {
                var block = m.Groups[1].Value;

                string Val(string label)
                {
                    var rx = new Regex($"{label}(?:;[^:]+)*:(.+)", RegexOptions.IgnoreCase);
                    var mm = rx.Match(block);
                    if (!mm.Success) return "";
                    var raw = mm.Groups[1].Value.Replace("\r", "").Replace("\n", "");
                    return DecodeQuotedPrintable(raw).Trim();
                }

                string fullName = Val("FN");
                string nameField = Val("N");
                string phone = Val("TEL");
                string email = Val("EMAIL");
                string bday = Val("BDAY");
                string adr = Val("ADR")?.Replace(";;", "");
                string org = Val("ORG");
                string note = Val("NOTE");

                if (string.IsNullOrWhiteSpace(fullName) &&
                    string.IsNullOrWhiteSpace(phone) &&
                    string.IsNullOrWhiteSpace(email))
                {
                    skipped++;
                    continue;
                }

                string firstName = null;
                string lastName = null;
                if (!string.IsNullOrWhiteSpace(nameField) && nameField.Contains(";"))
                {
                    var parts = nameField.Split(';');
                    lastName = parts[0];
                    firstName = parts.Length > 1 ? parts[1] : null;
                }

                if (string.IsNullOrWhiteSpace(firstName) && string.IsNullOrWhiteSpace(lastName))
                {
                    firstName = string.IsNullOrWhiteSpace(fullName) ? "Unnamed" : fullName;
                }

                string photoB64 = Val("PHOTO");
                string savedImg = null;
                if (!string.IsNullOrWhiteSpace(photoB64))
                {
                    try
                    {
                        var imgBytes = Convert.FromBase64String(photoB64);
                        var file = $"{Guid.NewGuid()}.jpg";
                        var path = Path.Combine("wwwroot/uploads", file);
                        Directory.CreateDirectory(Path.GetDirectoryName(path)!);
                        System.IO.File.WriteAllBytes(path, imgBytes);
                        savedImg = "/uploads/" + file;
                    }
                    catch
                    {
                        // Malformed photo is simply skipped
                    }
                }

                DateTime? birthDateParsed = null;
                if (!string.IsNullOrWhiteSpace(bday) &&
                    DateTime.TryParseExact(
                        bday,
                        "yyyy-MM-dd",
                        CultureInfo.InvariantCulture,
                        DateTimeStyles.None,
                        out var dt))
                {
                    birthDateParsed = dt;
                }

                list.Add(new Contact
                {
                    UserId = userId,
                    FirstName = firstName?.Trim(),
                    LastName = lastName?.Trim(),
                    Phone = phone,
                    Email = email,
                    Address = adr,
                    Company = org,
                    Tags = note,
                    BirthDate = birthDateParsed,
                    ContactImage = savedImg,
                    CreatedDate = DateTime.UtcNow
                });
            }

            return list;
        }

        private static string DecodeQuotedPrintable(string s)
        {
            if (!s.Contains('=')) return s;
            using var ms = new MemoryStream();
            for (int i = 0; i < s.Length; i++)
            {
                if (s[i] == '=' && i + 2 < s.Length &&
                    IsHexDigit(s[i + 1]) && IsHexDigit(s[i + 2]))
                {
                    string hex = s.Substring(i + 1, 2);
                    ms.WriteByte(Convert.ToByte(hex, 16));
                    i += 2;
                }
                else
                {
                    ms.WriteByte((byte)s[i]);
                }
            }
            return Encoding.UTF8.GetString(ms.ToArray());
        }

        private static bool IsHexDigit(char c) =>
            (c >= '0' && c <= '9') ||
            (c >= 'A' && c <= 'F') ||
            (c >= 'a' && c <= 'f');

        /*=====================================================
         *  EXCEL  (UNCHANGED LOGIC WITH LOGGING WRAP)
         *====================================================*/
        [HttpGet]
        public IActionResult ImportFromExcel()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to import from Excel.";
                    return RedirectToAction("Login", "Auth");
                }

                return View();
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ExcelImport,
                    $"Exception in ImportFromExcel GET: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts);

                TempData["Error"] = "Unable to load Excel import page.";
                return View();
            }
        }

        [HttpPost]
        public IActionResult ImportFromExcel(IFormFile excelFile)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
            {
                TempData["Error"] = "Please log in to import from Excel.";
                return RedirectToAction("Login", "Auth");
            }

            if (excelFile == null || excelFile.Length == 0)
            {
                TempData["Error"] = "Please upload a valid Excel file.";
                return View();
            }

            try
            {
                using var mem = new MemoryStream();
                excelFile.CopyTo(mem);
                using var wb = new XLWorkbook(mem);
                var ws = wb.Worksheet(1);
                var rows = ws.RangeUsed().RowsUsed().Skip(1).ToList();
                int addedCount = 0;

                foreach (var row in rows)
                {
                    var firstName = row.Cell(1).GetValue<string>()?.Trim();
                    var lastName = row.Cell(2).GetValue<string>()?.Trim();
                    var position = row.Cell(3).GetValue<string>()?.Trim();
                    var email = row.Cell(4).GetValue<string>()?.Trim();
                    var phone = row.Cell(5).GetValue<string>()?.Trim();
                    var address = row.Cell(6).GetValue<string>()?.Trim();
                    var company = row.Cell(7).GetValue<string>()?.Trim();
                    var tags = row.Cell(8).GetValue<string>()?.Trim();
                    var birthStr = row.Cell(9).GetString()?.Trim();

                    // Skip if no meaningful data
                    if (string.IsNullOrWhiteSpace(firstName) &&
                        string.IsNullOrWhiteSpace(lastName) &&
                        string.IsNullOrWhiteSpace(email) &&
                        string.IsNullOrWhiteSpace(phone))
                    {
                        continue;
                    }

                    DateTime? birthDate = null;
                    if (!string.IsNullOrWhiteSpace(birthStr) &&
                        DateTime.TryParse(birthStr, out var bd))
                    {
                        birthDate = bd;
                    }

                    var contact = new Contact
                    {
                        UserId = userId.Value,
                        FirstName = string.IsNullOrWhiteSpace(firstName) ? "Unnamed" : firstName,
                        LastName = lastName,
                        Position = position,
                        Email = email,
                        Phone = phone,
                        Address = address,
                        Company = company,
                        Tags = tags,
                        BirthDate = birthDate,
                        CreatedDate = DateTime.UtcNow
                    };

                    _context.Contacts.Add(contact);
                    addedCount++;
                }

                _context.SaveChanges();

                LogActivity(
                    LogAction.ExcelImport,
                    $"Imported {addedCount} contact(s) from Excel.",
                    LogStatus.Successful,
                    LogModule.Contacts,
                    userId);

                TempData["Message"] = $"Imported {addedCount} contact(s) from Excel.";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ExcelImport,
                    $"Exception during Excel import: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts,
                    userId);

                TempData["Error"] = "An error occurred while importing Excel file.";
                return View();
            }
        }

        [HttpGet]
        public IActionResult ExportToExcel()
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
            {
                TempData["Error"] = "Please log in to export contacts.";
                return RedirectToAction("Login", "Auth");
            }

            try
            {
                var contacts = _context.Contacts
                    .Where(c => c.UserId == userId.Value)
                    .ToList();

                using var wb = new XLWorkbook();
                var ws = wb.Worksheets.Add("Contacts");

                // Header row
                ws.Cell(1, 1).Value = "First Name";
                ws.Cell(1, 2).Value = "Last Name";
                ws.Cell(1, 3).Value = "Position";
                ws.Cell(1, 4).Value = "Email";
                ws.Cell(1, 5).Value = "Phone";
                ws.Cell(1, 6).Value = "Address";
                ws.Cell(1, 7).Value = "Company";
                ws.Cell(1, 8).Value = "Tags";
                ws.Cell(1, 9).Value = "BirthDate";

                // Data rows
                for (int i = 0; i < contacts.Count; i++)
                {
                    var c = contacts[i];
                    ws.Cell(i + 2, 1).Value = c.FirstName;
                    ws.Cell(i + 2, 2).Value = c.LastName;
                    ws.Cell(i + 2, 3).Value = c.Position;
                    ws.Cell(i + 2, 4).Value = c.Email;
                    ws.Cell(i + 2, 5).Value = c.Phone;
                    ws.Cell(i + 2, 6).Value = c.Address;
                    ws.Cell(i + 2, 7).Value = c.Company;
                    ws.Cell(i + 2, 8).Value = c.Tags;
                    ws.Cell(i + 2, 9).Value = c.BirthDate?.ToString("yyyy-MM-dd");
                }

                ws.Columns().AdjustToContents();

                using var mem = new MemoryStream();
                wb.SaveAs(mem);
                mem.Position = 0;

                LogActivity(
                    LogAction.ExcelExport,
                    $"Exported {contacts.Count} contacts to Excel.",
                    LogStatus.Successful,
                    LogModule.Contacts,
                    userId);

                return File(
                    mem.ToArray(),
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "contacts_export.xlsx");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ExcelExport,
                    $"Exception during Excel export: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Contacts,
                    userId);

                TempData["Error"] = "An error occurred while exporting contacts to Excel.";
                return RedirectToAction("Index");
            }
        }
    }
}