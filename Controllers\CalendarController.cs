﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AdressBookAppWeb.Controllers
{
    public class CalendarController : BaseController
    {
        public CalendarController(AppDbContext context) : base(context)
        {
        }

        [HttpGet]
        public IActionResult Index()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "You must be logged in to view the calendar.";
                    return RedirectToAction("Login", "Auth");
                }

                return View();
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ViewCalendar,
                    $"Exception in Calendar/Index GET: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Calendar);

                TempData["Error"] = "An error occurred while loading the calendar.";
                return View();
            }
        }

        [HttpGet]
        public IActionResult GetBirthdays()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    return Json(new List<object>());
                }

                var contacts = _context.Contacts
                    .Where(c => c.UserId == userId.Value && c.BirthDate != null)
                    .ToList();

                var events = new List<object>();
                var currentYear = DateTime.Now.Year;
                var years = new[] { currentYear - 1, currentYear, currentYear + 1 };

                foreach (var contact in contacts)
                {
                    foreach (var year in years)
                    {
                        var birthDate = contact.BirthDate.Value;
                        DateTime safeDate;
                        try
                        {
                            safeDate = new DateTime(year, birthDate.Month, birthDate.Day);
                        }
                        catch
                        {
                            safeDate = new DateTime(year, 2, 28);
                        }

                        events.Add(new
                        {
                            title = $"{contact.Name}'s Birthday",
                            start = safeDate.ToString("yyyy-MM-dd"),
                            extendedProps = new { contactId = contact.Id }
                        });
                    }
                }

                return Json(events);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.FetchBirthdays,
                    $"Exception in Calendar/GetBirthdays: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Calendar);

                return Json(new List<object>());
            }
        }
    }
}
