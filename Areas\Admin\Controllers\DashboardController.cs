﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using Microsoft.AspNetCore.Mvc;
using System.Linq;

namespace AdressBookAppWeb.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class DashboardController : BaseController
    {
        public DashboardController(AppDbContext context) : base(context)
        {
        }

        // GET: /Admin/Dashboard
        public async Task<IActionResult> Index()
        {
            try
            {
                // Count total users
                var totalUsers = _context.Users.Count();

                // Count total contact reports
                var contactReports = _context.ContactReports.Count();

                // Count new registrations in the last 30 days
                var newRegistrations = _context.Users
                    .Count(u => u.RegistrationDate > DateTime.Now.AddDays(-30));

                // Count total logs
                var logCount = _context.Logs.Count();

                ViewBag.TotalUsers = totalUsers;
                ViewBag.ContactReports = contactReports;
                ViewBag.NewRegistrations = newRegistrations;
                ViewBag.Logs = logCount;

                return View();
            }
            catch (Exception)
            {
                // If anything goes wrong, show 0 for each metric and display an error message
                ViewBag.TotalUsers = 0;
                ViewBag.ContactReports = 0;
                ViewBag.NewRegistrations = 0;
                ViewBag.Logs = 0;

                TempData["ErrorMessage"] = "An error occurred while loading dashboard metrics.";
                return View();
            }
        }
    }
}