﻿using Microsoft.AspNetCore.Mvc;
using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using AdressBookAppWeb.ViewModels;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using AdressBookAppWeb.Helpers;

namespace AdressBookAppWeb.Controllers
{
    public class AuthController : Controller
    {
        private readonly AppDbContext _context;
        private readonly EmailService _emailService;

        public AuthController(AppDbContext context, EmailService emailService)
        {
            _context = context;
            _emailService = emailService;
        }

        public IActionResult Index() => RedirectToAction("Login");

        [HttpGet]
        public IActionResult Login() => View();

        [HttpPost]
        public IActionResult Login(string email, string password)
        {
            try
            {
                email = email?.Trim().ToLower();
                password = password?.Trim();

                var user = _context.Users.FirstOrDefault(u => u.Email == email);
                if (user != null && PasswordHelper.VerifyPassword(password, user.Password))
                {
                    user.LastLoginDate = DateTime.Now;
                    _context.SaveChanges();

                    HttpContext.Session.SetInt32("UserId", user.Id);
                    HttpContext.Session.SetString("UserName", $"{user.FirstName} {user.LastName}");

                    Log(LogAction.LoggedIn, "User logged in successfully.", LogStatus.Successful, user.Id);

                    if (user.Role == UserRole.Admin || user.Role == UserRole.SuperAdmin)
                    {
                        return RedirectToAction("Index", "Dashboard", new { area = "Admin" });
                    }

                    return RedirectToAction("Index", "Dashboard");
                }

                ViewBag.Error = "Invalid email or password.";
                Log(LogAction.LoggedIn, $"Login failed for email: {email}", LogStatus.Error, null);
                return View();
            }
            catch (Exception ex)
            {
                Log(LogAction.LoggedIn, $"Login exception: {ex.Message}", LogStatus.Error, null);
                ViewBag.Error = "An unexpected error occurred.";
                return View();
            }
        }

        [HttpGet]
        public IActionResult Register() => View(new User());

        [HttpPost]
        public IActionResult Register(User user, string ConfirmPassword)
        {
            try
            {
                user.Email = user.Email?.Trim().ToLower();
                user.Phone = user.Phone?.Trim();

                if (_context.Users.Any(u => u.Email == user.Email))
                {
                    ModelState.AddModelError("Email", "Email is already registered.");
                    Log(LogAction.Register, $"Duplicate email registration attempt: {user.Email}", LogStatus.Error, null);
                    return View(user);
                }

                if (!string.IsNullOrEmpty(user.Phone) && _context.Users.Any(u => u.Phone == user.Phone))
                {
                    ModelState.AddModelError("Phone", "Phone number is already in use.");
                    Log(LogAction.Register, $"Duplicate phone registration attempt: {user.Phone}", LogStatus.Error, null);
                    return View(user);
                }

                if (user.Password != ConfirmPassword)
                {
                    ViewBag.Error = "Passwords do not match.";
                    return View(user);
                }

                if (user.Password.Length < 8)
                {
                    ViewBag.Error = "Password must be at least 8 characters long";
                    return View(user);
                }

                user.Password = PasswordHelper.HashPassword(user.Password);
                user.RegistrationDate = DateTime.Now;
                user.UserKey = Guid.NewGuid().ToString();
                user.Role = UserRole.User;

                _context.Users.Add(user);
                _context.SaveChanges();

                HttpContext.Session.SetInt32("UserId", user.Id);
                HttpContext.Session.SetString("UserName", $"{user.FirstName} {user.LastName}");

                Log(LogAction.Register, "User registered successfully.", LogStatus.Successful, user.Id);
                return RedirectToAction("Index", "Dashboard");
            }
            catch (DbUpdateException dbEx)
            {
                if (dbEx.InnerException?.Message.Contains("IX_Users_Email") == true)
                {
                    ModelState.AddModelError("Email", "This email is already registered.");
                }
                else if (dbEx.InnerException?.Message.Contains("IX_Users_Phone") == true)
                {
                    ModelState.AddModelError("Phone", "This phone number is already in use.");
                }
                return View(user);
            }
            catch (Exception ex)
            {
                Log(LogAction.Register, $"Registration exception: {ex.Message}", LogStatus.Error, null);
                ViewBag.Error = "An unexpected error occurred.";
                return View(user);
            }
        }

        [HttpGet]
        public IActionResult ResetPass() => View();

        [HttpPost]
        public async Task<IActionResult> ResetPass(string email)
        {
            try
            {
                var user = _context.Users.FirstOrDefault(u => u.Email == email);
                if (user == null)
                {
                    ModelState.AddModelError("", "Email not found.");
                    Log(LogAction.ResetPassword, $"Email not found: {email}", LogStatus.Error, null);
                    return View();
                }

                user.PasswordResetToken = Guid.NewGuid().ToString();
                user.TokenExpiry = DateTime.UtcNow.AddHours(1);
                _context.SaveChanges();

                var resetLink = Url.Action("ResetPassEnd", "Auth", new { token = user.PasswordResetToken }, Request.Scheme);
                var body = $"Click to reset your password: {resetLink}";

                await _emailService.SendEmailAsync(user.Email, "Password Reset", body);

                TempData["Success"] = "An email has been sent to reset your password.";
                Log(LogAction.ResetPassword, $"Reset email sent to: {email}", LogStatus.Successful, user.Id);
                return RedirectToAction("Login");
            }
            catch (Exception ex)
            {
                Log(LogAction.ResetPassword, $"Reset email exception: {ex.Message}", LogStatus.Error, null);
                ViewBag.Error = "Error processing your request.";
                return View();
            }
        }

        [HttpGet]
        public IActionResult ResetPassEnd(string token)
        {
            try
            {
                var user = _context.Users.FirstOrDefault(u => u.PasswordResetToken == token && u.TokenExpiry > DateTime.UtcNow);
                if (user == null)
                {
                    TempData["Error"] = "Invalid or expired token.";
                    Log(LogAction.ResetPassword, $"Invalid reset token: {token}", LogStatus.Error, null);
                    return RedirectToAction("ResetPass");
                }

                ViewBag.Token = token;
                return View();
            }
            catch (Exception ex)
            {
                Log(LogAction.ResetPassword, $"Reset token lookup failed: {ex.Message}", LogStatus.Error, null);
                TempData["Error"] = "Error processing reset request.";
                return RedirectToAction("ResetPass");
            }
        }

        [HttpPost]
        public IActionResult ResetPassEnd(string token, string newPassword, string confirmPassword)
        {
            try
            {
                if (newPassword != confirmPassword)
                {
                    TempData["Error"] = "Passwords do not match.";
                    ViewBag.Token = token;
                    return View();
                }

                var user = _context.Users.FirstOrDefault(u => u.PasswordResetToken == token && u.TokenExpiry > DateTime.UtcNow);
                if (user == null)
                {
                    TempData["Error"] = "Invalid or expired token.";
                    return RedirectToAction("ResetPass");
                }

                user.Password = newPassword;
                user.PasswordResetToken = null;
                user.TokenExpiry = null;
                _context.SaveChanges();

                TempData["Success"] = "Password reset successfully.";
                Log(LogAction.ResetPassword, $"Password reset for user ID {user.Id}", LogStatus.Successful, user.Id);
                return RedirectToAction("Login");
            }
            catch (Exception ex)
            {
                Log(LogAction.ResetPassword, $"Password reset error: {ex.Message}", LogStatus.Error, null);
                TempData["Error"] = "An error occurred while resetting your password.";
                ViewBag.Token = token;
                return View();
            }
        }

        [HttpGet]
        public IActionResult Support() => View();

        [HttpPost]
        public IActionResult Support(ContactReportViewModel vm)
        {
            try
            {
                if (!ModelState.IsValid)
                    return View(vm);

                var report = new ContactReport
                {
                    SenderName = vm.SenderName,
                    Email = vm.Email,
                    Subject = vm.Subject,
                    Message = vm.Message,
                    ReportDate = DateTime.Now,
                    Status = ReportStatus.Pending,
                    UserId = null
                };

                _context.ContactReports.Add(report);
                _context.SaveChanges();

                ViewBag.Success = "Support message sent.";
                Log(LogAction.MailSending, "Support message logged.", LogStatus.Successful, null);
                ModelState.Clear();
                return View();
            }
            catch (Exception ex)
            {
                Log(LogAction.MailSending, $"Support message error: {ex.Message}", LogStatus.Error, null);
                ViewBag.Error = "Failed to send support message.";
                return View(vm);
            }
        }

        public IActionResult Logout()
        {
            try
            {
                var userId = GetCurrentUserId();
                HttpContext.Session.Clear();
                Log(LogAction.LoggedOut, "User logged out.", LogStatus.Successful, userId);
                return RedirectToAction("Login");
            }
            catch (Exception ex)
            {
                Log(LogAction.LoggedOut, $"Logout error: {ex.Message}", LogStatus.Error, null);
                return RedirectToAction("Login");
            }
        }

        private void Log(LogAction action, string message, LogStatus status, int? userId)
        {
            try
            {
                _context.Logs.Add(new Log
                {
                    Action = action,
                    Module = LogModule.Auth,
                    Message = message,
                    Status = status,
                    Timestamp = DateTime.Now,
                    IPAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "N/A",
                    UserId = userId
                });
                _context.SaveChanges();
            }
            catch { /* Logging failure is silently ignored */ }
        }

        private int? GetCurrentUserId() => HttpContext.Session.GetInt32("UserId");
    }
}