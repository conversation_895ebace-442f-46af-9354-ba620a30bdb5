﻿@model AdressBookAppWeb.Models.User

@{
    Layout = "_AuthLayout";
    ViewData["Title"] = "Register";
}

<div class="row justify-content-center">
    <div class="col-lg-7">
        <div class="card shadow-lg border-0 rounded-lg mt-5">
            <div class="card-header">
                <h3 class="text-center font-weight-light my-4">Create Account</h3>
            </div>
            <div class="card-body">
                @if (ViewBag.Error != null)
                {
                    <div class="alert alert-danger">@ViewBag.Error</div>
                }

                <form method="post" asp-action="Register">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0">
                                <input asp-for="FirstName" class="form-control" placeholder="Enter your first name" required />
                                <label asp-for="FirstName">First name</label>
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input asp-for="LastName" class="form-control" placeholder="Enter your last name" required />
                                <label asp-for="LastName">Last name</label>
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-floating mb-3">
                        <input asp-for="Email" class="form-control" placeholder="<EMAIL>" required />
                        <label asp-for="Email">Email address</label>
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>

                    <div class="form-floating mb-3">
                        <input asp-for="Phone" class="form-control" placeholder="Phone number" required />
                        <label asp-for="Phone">Phone</label>
                        <span asp-validation-for="Phone" class="text-danger"></span>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0">
                                <input asp-for="Password" class="form-control" placeholder="Create a password" required />
                                <label asp-for="Password">Password</label>
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0">
                                <input class="form-control" id="ConfirmPassword" name="ConfirmPassword" type="password" placeholder="Confirm password" required />
                                <label for="ConfirmPassword">Confirm Password</label>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 mb-0">
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-block">Create Account</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <div class="small">
                    <a asp-controller="Auth" asp-action="Login">Have an account? Go to login</a>
                </div>
                <div class="small mt-2">
                    <a asp-controller="Auth" asp-action="Support">Need help creating an account? Contact Support</a>
                </div>
            </div>
        </div>
    </div>
</div>