﻿@model List<AdressBookAppWeb.Models.SharedAccess>

@{
    ViewData["Title"] = "Contacts Shared With Me";
    var currentPage = (int)(ViewBag.CurrentPage ?? 1);
    var totalPages = (int)(ViewBag.TotalPages ?? 1);
    var searchTerm = ViewBag.SearchTerm as string ?? "";
}

<div class="container-fluid px-4">
    <h2 class="mt-4">Contacts Shared With Me</h2>

    @* Alerts *@
    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success">@TempData["Message"]</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <ul class="nav nav-tabs" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link" asp-action="Index" asp-controller="SharedAccess">Manage Access</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link active" asp-action="ContactsSharedWithMe" asp-controller="SharedAccess">Contacts Shared With Me</a>
        </li>
    </ul>

    <button class="btn btn-secondary my-3" data-bs-toggle="offcanvas" data-bs-target="#filterViewShared">
        Filter Owners
    </button>

    <div class="row gy-3">
        @foreach (var access in Model)
        {
            <div class="col-sm-6 col-md-4">
                <div class="card shadow-sm d-flex flex-row align-items-center p-2" style="border-radius: 8px;">
                    <img src="@(!string.IsNullOrEmpty(access.Owner?.ProfileImage) ? access.Owner.ProfileImage : "/assets/img/default.png")"
                         class="rounded" style="height: 80px; width: 80px; object-fit: cover;"
                         alt="Owner Image" />
                    <div class="ms-3 w-100">
                        <div>
                            <h6 class="mb-0">@access.Owner.FirstName @access.Owner.LastName</h6>
                            <p class="mb-0">@access.Owner.Email</p>
                        </div>
                        <div class="d-flex gap-1 mt-2">
                            <a asp-controller="SharedAccess"
                               asp-action="Contacts"
                               asp-route-ownerId="@access.OwnerId"
                               class="btn btn-sm btn-primary">
                                View Contacts
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>

    @if (totalPages > 1)
    {
        <div class="d-flex justify-content-center mt-4">
            <nav aria-label="Pagination">
                <ul class="pagination pagination-sm mb-0">
                    <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                        <a class="page-link" asp-action="ContactsSharedWithMe"
                           asp-route-page="@(currentPage - 1)"
                           asp-route-searchTerm="@searchTerm">Previous</a>
                    </li>

                    @for (int i = 1; i <= totalPages; i++)
                    {
                        <li class="page-item @(i == currentPage ? "active" : "")">
                            <a class="page-link" asp-action="ContactsSharedWithMe"
                               asp-route-page="@i"
                               asp-route-searchTerm="@searchTerm">@i</a>
                        </li>
                    }

                    <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                        <a class="page-link" asp-action="ContactsSharedWithMe"
                           asp-route-page="@(currentPage + 1)"
                           asp-route-searchTerm="@searchTerm">Next</a>
                    </li>
                </ul>
            </nav>
        </div>
    }
</div>

@* Offcanvas sidebar for filtering ContactsSharedWithMe *@
<div class="offcanvas offcanvas-end" tabindex="-1" id="filterViewShared" aria-labelledby="filterViewSharedLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="filterViewSharedLabel">Filter Owners</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <form method="get" asp-action="ContactsSharedWithMe">
            <div class="mb-3">
                <label for="viewSharedOwner" class="form-label">Owner Fullname or Email</label>
                <input type="text" id="viewSharedOwner" name="searchTerm" class="form-control"
                       placeholder="Enter owner name or email..." value="@searchTerm" />
            </div>
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">Search</button>
                <a asp-action="ContactsSharedWithMe" class="btn btn-secondary">Reset</a>
            </div>
        </form>
    </div>
</div>