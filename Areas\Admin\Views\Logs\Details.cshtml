﻿@model AdressBookAppWeb.Models.Log

@{
    ViewData["Title"] = "Log Details";
}

<div class="container mt-4">
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <h2>Log Details</h2>
    <div class="card mb-4">
        <div class="card-body">
            <dl class="row">
                <dt class="col-sm-3">ID</dt>
                <dd class="col-sm-9">@Model.Id</dd>

                <dt class="col-sm-3">User ID</dt>
                <dd class="col-sm-9">
                    @if (Model.User != null)
                    {
                        @Model.User.Id
                    }
                    else
                    {
                        <em>Anonymous</em>
                    }
                </dd>

                <dt class="col-sm-3">Module</dt>
                <dd class="col-sm-9">@Model.Module</dd>

                <dt class="col-sm-3">Action</dt>
                <dd class="col-sm-9">@Model.Action</dd>

                <dt class="col-sm-3">Status</dt>
                <dd class="col-sm-9">@Model.Status</dd>

                <dt class="col-sm-3">Timestamp</dt>
                <dd class="col-sm-9">@Model.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")</dd>

                <dt class="col-sm-3">IP Address</dt>
                <dd class="col-sm-9">@Model.IPAddress</dd>

                <dt class="col-sm-3">User</dt>
                <dd class="col-sm-9">
                    @if (Model.User != null)
                    {
                        @($"{Model.User.FirstName} {Model.User.LastName} ({Model.User.Email})")
                    }
                    else
                    {
                        <em>Anonymous</em>
                    }
                </dd>

                <dt class="col-sm-3">Message</dt>
                <dd class="col-sm-9">@Model.Message</dd>
            </dl>
        </div>
    </div>

    <a class="btn btn-secondary" asp-action="Index">Back to List</a>
</div>