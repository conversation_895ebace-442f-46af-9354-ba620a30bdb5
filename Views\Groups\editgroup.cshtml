﻿@model AdressBookAppWeb.ViewModels.EditGroupViewModel
@{
    ViewData["Title"] = "Edit Group";
}

<div class="container-fluid px-4 pb-4">
    <h2 class="mt-4 mb-4">Edit Group</h2>

    @* Alerts *@
    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success">@TempData["Message"]</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <form asp-action="EditGroup" method="post">
        @Html.AntiForgeryToken()
        <input type="hidden" asp-for="Id" />

        <div class="mb-3">
            <label asp-for="GroupName" class="form-label">Group Name*</label>
            <input asp-for="GroupName" class="form-control" placeholder="Enter group name" />
            <span asp-validation-for="GroupName" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="GroupDescription" class="form-label">Description</label>
            <textarea asp-for="GroupDescription" class="form-control" rows="3" placeholder="Enter description (optional)"></textarea>
            <span asp-validation-for="GroupDescription" class="text-danger"></span>
        </div>

        <button type="submit" class="btn btn-primary">Save Changes</button>
        <a asp-action="Index" class="btn btn-secondary ms-2">Cancel</a>
    </form>
</div>