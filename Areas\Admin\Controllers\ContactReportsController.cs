﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AdressBookAppWeb.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class ContactReportsController : BaseController
    {
        private const int PageSize = 10;
        private readonly EmailService _emailService;

        public ContactReportsController(AppDbContext context, EmailService emailService) : base(context)
        {
            _emailService = emailService;
        }

        // GET: /Admin/ContactReports
        public async Task<IActionResult> Index(string sender, string email, string subject, string status, DateTime? date, int page = 1)
        {
            try
            {
                var query = _context.ContactReports.AsQueryable();

                if (!string.IsNullOrEmpty(sender))
                    query = query.Where(r => r.SenderName.Contains(sender));

                if (!string.IsNullOrEmpty(email))
                    query = query.Where(r => r.Email.Contains(email));

                if (!string.IsNullOrEmpty(subject))
                    query = query.Where(r => r.Subject.Contains(subject));

                if (!string.IsNullOrEmpty(status) && Enum.TryParse<ReportStatus>(status, true, out var parsedStatus))
                    query = query.Where(r => r.Status == parsedStatus);

                if (date.HasValue)
                    query = query.Where(r => r.ReportDate.Date == date.Value.Date);

                var totalItems = await query.CountAsync();
                var reports = await query
                    .OrderByDescending(r => r.ReportDate)
                    .Skip((page - 1) * PageSize)
                    .Take(PageSize)
                    .ToListAsync();

                ViewBag.TotalPages = (int)Math.Ceiling(totalItems / (double)PageSize);
                ViewBag.CurrentPage = page;
                ViewBag.Filters = new { sender, email, subject, status, date };

                return View(reports);
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while loading the contact reports.";
                return View(Enumerable.Empty<ContactReport>());
            }
        }

        // GET: /Admin/ContactReports/Detail/5
        public async Task<IActionResult> Detail(int id)
        {
            try
            {
                var report = await _context.ContactReports.FindAsync(id);
                if (report == null)
                    return NotFound();

                return View(report);
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while loading the report.";
                return RedirectToAction("Index");
            }
        }

        // POST: /Admin/ContactReports/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var report = await _context.ContactReports.FindAsync(id);
                if (report == null)
                    return NotFound();

                _context.ContactReports.Remove(report);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "Report deleted successfully.";
                return RedirectToAction("Index");
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while deleting the report.";
                return RedirectToAction("Index");
            }
        }

        // POST: /Admin/ContactReports/UpdateStatus/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateStatus(int id, ReportStatus status)
        {
            try
            {
                var report = await _context.ContactReports.FindAsync(id);
                if (report == null)
                    return NotFound();

                report.Status = status;

                if (status == ReportStatus.Reviewed)
                {
                    report.ReviewDate = DateTime.Now;
                }

                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "Status updated successfully.";
                return RedirectToAction("Detail", new { id });
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while updating the status.";
                return RedirectToAction("Detail", new { id });
            }
        }

        // POST: /Admin/ContactReports/SendReply
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SendReply(int reportId, string emailTo, string subject, string message)
        {
            try
            {
                var report = await _context.ContactReports.FindAsync(reportId);
                if (report == null)
                    return NotFound();

                // Validate inputs
                if (string.IsNullOrWhiteSpace(emailTo))
                    ModelState.AddModelError("emailTo", "The recipient email is required.");
                if (string.IsNullOrWhiteSpace(subject))
                    ModelState.AddModelError("subject", "Subject cannot be empty.");

                if (!ModelState.IsValid)
                {
                    // Re‐display the Detail view with validation errors
                    return View("Detail", report);
                }

                // Update the report in database
                report.AdminResponse = message;
                report.ReviewDate = DateTime.Now;
                if (report.Status == ReportStatus.Pending)
                    report.Status = ReportStatus.Reviewed;

                await _context.SaveChangesAsync();

                // Send the actual email
                await _emailService.SendEmailAsync(emailTo, subject, message);

                TempData["SuccessMessage"] = "Reply sent and saved successfully.";
                return RedirectToAction("Detail", new { id = reportId });
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "An error occurred while sending the reply.";
                return RedirectToAction("Detail", new { id = reportId });
            }
        }
    }
}