﻿using AdressBookAppWeb.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace AdressBookAppWeb.ViewModels
{
    public class UserCreateViewModel
    {
        [Required]
        public string FirstName { get; set; }

        [Required]
        public string LastName { get; set; }

        [Required, EmailAddress]
        public string Email { get; set; }

        [Required]
        public string Phone { get; set; }

        [Required]
        public string Password { get; set; }

        public IFormFile? ProfileImage { get; set; }

        [Required]
        public UserRole Role { get; set; }
    }
}