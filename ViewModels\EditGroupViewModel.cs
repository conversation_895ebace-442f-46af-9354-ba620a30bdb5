﻿using System.ComponentModel.DataAnnotations;

namespace AdressBookAppWeb.ViewModels
{
    public class EditGroupViewModel
    {
        [Required]
        public int Id { get; set; }

        [Required(ErrorMessage = "Group name is required.")]
        [Display(Name = "Group Name")]
        public string GroupName { get; set; }

        [Display(Name = "Group Description")]
        public string GroupDescription { get; set; }
    }
}