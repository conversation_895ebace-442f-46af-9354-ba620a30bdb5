﻿using AdressBookAppWeb.Data;
using AdressBookAppWeb.Models;
using AdressBookAppWeb.Models.Enums;
using AdressBookAppWeb.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Hosting;
using System;
using System.IO;
using System.Linq;
using AdressBookAppWeb.Helpers;

namespace AdressBookAppWeb.Controllers
{
    public class ProfileController : BaseController
    {
        private readonly IWebHostEnvironment _env;

        public ProfileController(AppDbContext context, IWebHostEnvironment env) : base(context)
        {
            _env = env;
        }

        [HttpGet]
        public IActionResult Index()
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to view your profile.";
                    return RedirectToAction("Login", "Auth");
                }

                var user = _context.Users
                    .Include(u => u.ContactReports)
                    .FirstOrDefault(u => u.Id == userId.Value);

                if (user == null)
                {
                    TempData["Error"] = "User not found.";
                    return RedirectToAction("Login", "Auth");
                }

                ViewBag.User = user;

                var model = new UserProfileViewModel
                {
                    Id = user.Id,
                    Email = user.Email,
                    Phone = user.Phone,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    CurrentImagePath = user.ProfileImage
                };

                return View(model);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ViewProfile,
                    $"Exception in Index GET: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Auth);

                TempData["Error"] = "An error occurred while loading your profile.";
                return RedirectToAction("Index", "Dashboard");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult UpdateProfile(UserProfileViewModel model)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
            {
                TempData["Error"] = "Please log in to update your profile.";
                return RedirectToAction("Login", "Auth");
            }

            try
            {
                if (!ModelState.IsValid)
                {
                    ViewBag.User = _context.Users
                        .Include(u => u.ContactReports)
                        .FirstOrDefault(u => u.Id == model.Id);
                    return View("Index", model);
                }

                var user = _context.Users.FirstOrDefault(u => u.Id == model.Id && u.Id == userId.Value);
                if (user == null)
                {
                    TempData["Error"] = "User not found.";
                    return RedirectToAction("Index");
                }

                user.FirstName = model.FirstName;
                user.LastName = model.LastName;
                user.Email = model.Email;
                user.Phone = model.Phone;

                if (model.ProfileImage != null)
                {
                    var uploadsFolder = Path.Combine(_env.WebRootPath, "uploads");
                    Directory.CreateDirectory(uploadsFolder);

                    var fileName = $"{Guid.NewGuid()}_{Path.GetFileName(model.ProfileImage.FileName)}";
                    var filePath = Path.Combine(uploadsFolder, fileName);
                    using var stream = new FileStream(filePath, FileMode.Create);
                    model.ProfileImage.CopyTo(stream);

                    user.ProfileImage = "/uploads/" + fileName;
                }

                _context.SaveChanges();
                TempData["Success"] = "Profile updated successfully.";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.UpdateProfile,
                    $"Exception in UpdateProfile POST: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Auth,
                    userId.Value);

                TempData["Error"] = "An error occurred while updating your profile.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult ChangePassword(ChangePasswordViewModel model)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
            {
                TempData["PasswordError"] = "Please log in to change your password.";
                return RedirectToAction("Login", "Auth");
            }

            try
            {
                if (!ModelState.IsValid)
                {
                    TempData["PasswordError"] = "Please correct the password form.";
                    return RedirectToAction("Index");
                }

                var user = _context.Users.FirstOrDefault(u => u.Id == model.Id && u.Id == userId.Value);
                if (user == null)
                {
                    TempData["PasswordError"] = "User not found.";
                    return RedirectToAction("Index");
                }

                if (!PasswordHelper.VerifyPassword(model.CurrentPassword, user.Password))
                {
                    TempData["PasswordError"] = "Current password is incorrect.";
                    return RedirectToAction("Index");
                }

                user.Password = PasswordHelper.HashPassword(model.NewPassword);
                _context.SaveChanges();

                TempData["PasswordSuccess"] = "Password updated successfully.";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ChangePassword,
                    $"Exception in ChangePassword POST: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Auth,
                    userId.Value);

                TempData["PasswordError"] = "An error occurred while changing your password.";
                return RedirectToAction("Index");
            }
        }

        [HttpGet]
        public IActionResult Report(int id)
        {
            try
            {
                var userId = HttpContext.Session.GetInt32("UserId");
                if (userId == null)
                {
                    TempData["Error"] = "Please log in to view the report.";
                    return RedirectToAction("Login", "Auth");
                }

                var report = _context.ContactReports
                    .Include(r => r.User)
                    .FirstOrDefault(r => r.Id == id && r.UserId == userId.Value);

                if (report == null)
                {
                    TempData["Error"] = "Report not found or you do not have permission to view it.";
                    return RedirectToAction("Index");
                }

                return View(report);
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.ViewReport,
                    $"Exception in Report GET: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Auth,
                    HttpContext.Session.GetInt32("UserId"));

                TempData["Error"] = "An error occurred while loading the report.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult CancelReport(int id)
        {
            var userId = HttpContext.Session.GetInt32("UserId");
            if (userId == null)
            {
                TempData["Error"] = "Please log in to cancel the report.";
                return RedirectToAction("Login", "Auth");
            }

            try
            {
                var report = _context.ContactReports
                    .FirstOrDefault(r => r.Id == id && r.UserId == userId.Value);

                if (report != null)
                {
                    _context.ContactReports.Remove(report);
                    _context.SaveChanges();
                    TempData["Success"] = "Report canceled successfully.";
                }
                else
                {
                    TempData["Error"] = "Report not found or you do not have permission to cancel it.";
                }

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                LogActivity(
                    LogAction.CancelReport,
                    $"Exception in CancelReport POST: {ex.Message}",
                    LogStatus.Error,
                    LogModule.Auth,
                    userId.Value);

                TempData["Error"] = "An error occurred while canceling the report.";
                return RedirectToAction("Index");
            }
        }
    }
}
